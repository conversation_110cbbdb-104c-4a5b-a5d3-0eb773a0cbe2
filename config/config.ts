import { defineConfig } from 'umi'
import routes from './routes'

export default defineConfig({
  publicPath: '/',
  base: '/',
  favicon: '/favicon.png',
  nodeModulesTransform: {
    type: 'none'
  },
  theme: {
    'primary-color': '#009995'
  },
  hash: true,
  locale: {
    antd: true,
    default: 'th-TH',
    baseNavigator: true
  },
  history: {
    type: 'browser'
  },
  layout: {
    locale: true,
    pwa: false
  },
  // 加载loading页面
  dynamicImport: {
    loading: '@/loading'
  },
  // exportStatic: {},
  mfsu: {},
  webpack5: {},
  define: {
    'process.env.API_ENV': process.env.API_ENV,
    'process.env.ULIVE_PATH': '/ulive',
    'process.env.UCHOICE_PATH': '/uchoice',
    'process.env.APP_NAME': 'achoice'
  },
  scripts: [
    'https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/svg_15404_12.2d46b3a11dd3b3200fe0b18f9412da35.js'
  ],
  routes,
  devtool: 'source-map'
  // mock: {}
})
