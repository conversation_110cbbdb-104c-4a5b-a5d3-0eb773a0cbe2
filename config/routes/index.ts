import commodity from './commodity'
import dataAnalytics from './dataAnalytics'
import loginRoute from './login'
import sampleApply from './sampleApply'
import machineReview from './machineReview'
import accountManage from './accountManage'

const routes: IRouteProps[] = [
  {
    path: '/',
    redirect: '/sampleApply'
  },
  ...loginRoute,
  ...sampleApply,
  ...machineReview,
  // ...dataAnalytics,
  ...commodity,
  // ...accountManage,
  {
    layout: false,
    component: './404'
  }
]

export default routes
