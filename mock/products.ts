export default {
  'GET /api-mall/aChoice/ops/getChoiceItemList': {
    code: 200,
    result: {
      list: [
        {
          itemId: '123123123',
          mainImage: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
          brand: 'Youpik',
          categoryName: '服装',
          isSelected: false,
          lazadaUrl: 'https://www.baidu.com',
          limitStock: 30,
          maxPrice: 100,
          minPrice: 50,
          remark: '这是备注',
          spec: '颜色*尺寸',
          stock: 50,
          title: '皮夹克'
        }
      ],
      total: 2
    },
    message: '',
    success: true
  } as IResponseListProps<API.ChoiceProductItem>,
  'GET /api-mall/aChoice/ops/getChoiceSkuList': {
    code: 200,
    result: {
      skuInfos: [
        {
          image: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
          limitStock: 30,
          price: 50,
          priceChoice: 50,
          skuId: '122123123',
          skuName: '皮夹克-蓝',
          stock: 25,
          totalStock: 25
        }
      ]
    },
    message: '',
    success: true
  } as IResponseProps<API.ChoiceSku>,
  'GET /api-mall/aChoice/ops/getShopList': {
    code: 200,
    result: [
      {
        shopCode: 'youpik',
        shopName: 'youpik'
      }
    ],
    message: '',
    success: true
  } as IResponseProps<API.TikTokShop[]>
}
