{"name": "tiktok-seller-center", "version": "1.0.0", "description": "tiktok-seller-center", "author": "h", "private": true, "scripts": {"dev:dev": "cross-env API_ENV=DEV umi dev", "dev:prod": "cross-env API_ENV=PROD umi dev", "dev:staging": "cross-env API_ENV=STAGING umi dev", "dev:pre": "cross-env API_ENV=PRE umi dev", "build": "umi build", "build:dev": "cross-env API_ENV=DEV umi build", "build:staging": "cross-env API_ENV=STAGING umi build", "build:pre": "cross-env API_ENV=PRE umi build", "lint": "lint-staged", "lint:commit-msg": "fabric verify-commit", "husky-install": "husky install", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "dependencies": {"@ant-design/icons": "^5.1.0", "@ant-design/pro-components": "^1.1.9", "@ant-design/pro-layout": "^6.5.0", "@sentry/react": "^7.103.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.7.11", "antd": "^4.24.16", "antd-v5": "npm:antd@^5.22.2", "axios": "^0.27.2", "classnames": "^2.3.1", "dayjs": "^1.11.9", "exceljs": "^4.3.0", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "mark.js": "^8.11.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "qs": "^6.11.0", "react": "17.x", "react-dom": "17.x", "react-turnstile": "^1.1.3", "umi": "^3.5.28"}, "devDependencies": {"@sentry/cli": "^2.28.6", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.182", "@types/qs": "^6.9.7", "@types/react": "^18.2.23", "@types/react-dom": "^17.0.0", "@umijs/fabric": "^2.12.2", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.28", "ali-oss": "^6.17.1", "cross-env": "^7.0.3", "husky": "^8.0.1", "less": "^4.2.1", "less-loader": "^11.0.0", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "stylelint": "^13.0.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}}