/*
 * @Author: <PERSON>
 * @Date: 2024-04-17 14:37:57
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-17 14:46:33
 * @Desc 页面标题hoc
 */

import type { ElementType } from 'react'
import styles from './widthPageTitle.less'

const withPageTitle = (Component: ElementType, title: string) => {
  return (props: any) => {
    return (
      <>
        <p className={styles.title}>{title}</p>
        <Component {...props} />
      </>
    )
  }
}

export default withPageTitle
