import { userInfoStorage } from './utils/storage'

export default (initialState: IInitialStateProps) => {
  // 在这里按照初始化数据定义项目中的权限，统一管理
  // 参考文档 https://next.umijs.org/docs/max/access
  const { id } = JSON.parse(userInfoStorage.get() || '{}')
  const isAuthShop = localStorage.getItem('isAuthShop') === 'true'
  return {
    canOporation: id === '34495407390742' || id === '47017749708844',
    isAuthShop: isAuthShop
  }
}
