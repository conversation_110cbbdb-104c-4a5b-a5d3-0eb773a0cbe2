import http from '@/services/http'

export interface GetVideoPerformanceParams {
  /**
   * 统计周期结束时间

   */
  endTime?: number
  /**
   * 视频投流状态 2-未投流 1-已投流 3-已关闭 99-全部
   */
  investmentFlowStatus?: 1 | 2 | 3 | 99
  /*
   * 素材类型 1-纯佣+付费素材 2-纯佣素材 0-未填写 99-全部
   */
  materialType?: 0 | 1 | 2 | 3 | 99
  /**
   * 搜索内容
   */
  param?: string

  /**
   * 搜索类型：1-商品 id 2-视频标题 3-视频 Id 4-达人名称
   */
  searchType?: 1 | 2 | 3 | 4
  /**
   * 统计周期开始时间
   */
  startTime?: number
  /**
   * 视频发布结束时间
   */
  videoPublishEndTime?: number
  /**
   * 视频发布开始时间
   */
  videoPublishStartTime?: number
}
export interface GetPositionCosteParams {
  id?: number
}
export interface GetExportingRecordsParams {
  activityId?: number
  /**
   * 类型：1 ：ops普通商品 2 :ops ypc商品 3 : sc普通商品 4.Uchoice订单导出记录 5:销售报表 6:达人分析
   */
  childrenType?: 1 | 2 | 3 | 4 | 5 | 6
  id?: number
  pageNo?: number
  pageSize?: number
  /**
   * 类型：1 ：导入 2 : 导出
   */
  type: 1 | 2
}
export interface UpdateCostParams {
  fee?: number
  id?: number
  materialType?: number
}

// export interface UpdateSampleNum {
//   sampleItems:[]
// }
/**
 * 获取视频业绩分析数据
 */
export const videoPerformanceList = (params: GetVideoPerformanceParams) => {
  return http.get<GetVideoPerformanceParams, IResponseProps<any>>(
    'api-uchoice/merchant/videoPerformance',
    params
  )
}

//获取视频业绩分析数据卡片内容
export const videoPerformanceOverview = (params: GetVideoPerformanceParams) => {
  return http.get<GetVideoPerformanceParams, IResponseProps<any>>(
    'api-uchoice/merchant/videoPerformanceOverview',
    params
  )
}

/**
 * 视频业绩分析数据导出
 */
export const videoPerformanceExport = (params: GetVideoPerformanceParams) => {
  return http.get<GetVideoPerformanceParams, IResponseProps<any>>(
    '/api-uchoice/merchant/videoPerformanceExport',
    params
  )
}

/**
 * 获取坑位费
 */
export const positionCost = (params: GetPositionCosteParams) => {
  return http.get<GetPositionCosteParams, IResponseProps<any>>(
    '/api-uchoice/merchant/positionCost',
    params
  )
}

/**
 * 获取样品成本
 */
export const sampleCost = (params: GetPositionCosteParams) => {
  return http.get<GetPositionCosteParams, IResponseProps<any>>(
    '/api-uchoice/merchant/sampleCost',
    params
  )
}

/**
 * 获取导入导出记录
 */
export const getExportingRecords = (params?: GetExportingRecordsParams) => {
  return http.get<GetExportingRecordsParams, IResponseProps<any>>(
    '/api-base/export/getExportingRecords',
    params
  )
}

/**
 * 修改坑位费与样品成本
 */
export const updateCost = (params?: UpdateCostParams) => {
  return http.postJSON<UpdateCostParams, IResponseProps<any>>(
    '/api-uchoice/merchant/updateCost',
    params
  )
}

/**
 * 修改寄样数量
 */
export const editSampleNum = (params?: any) => {
  return http.postJSON<any, IResponseProps<any>>('/api-uchoice/merchant/editSampleNum', params)
}

/**
 * 获取寄样成本
 */

export const getSampleCost = (params?: any) => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/merchant/getSampleCost', params)
}
//查询商家店铺列表
export const validShops = () => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/merchant/sample/suppliers/validShops')
}
//获取供应商信息
export const suppliersInfo = () => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/merchant/shop/suppliers/info')
}

//获取视频业绩分析数据(demo)

export const videoPerformanceMock = (params: GetVideoPerformanceParams) => {
  return http.get<GetVideoPerformanceParams, IResponseProps<any>>(
    '/api-uchoice/merchant/videoPerformanceMock',
    params
  )
}

//获取视频业绩分析数据(demo)
export const videoPerformanceOverviewMock = () => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/merchant/videoPerformanceOverviewMock')
}

//是否有子账号
export const haveShopSubAccount = () => {
  return http.get<any, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/suppliers/haveShopSubAccount'
  )
}
