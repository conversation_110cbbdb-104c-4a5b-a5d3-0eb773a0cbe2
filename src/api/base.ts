import http from '../services/http'

export interface GetShortLinkParams {
  url: string
  type?: number
}

export const getShortLink = async (data: GetShortLinkParams): Promise<IResponseProps<string>> => {
  return http.post<GetShortLinkParams>('/api-base/link/getLongLink', { ...data, type: 6 })
}

export interface GetExportingRecordsParams {
  /**
   * activityId
   */
  activityId?: number;
  /**
   * 类型：1 ：ops普通商品  2 :ops ypc商品  3 : sc普通商品 4.Uchoice订单导出记录 5:销售报表 6:达人分析
   */
  childrenType?: number;
  /**
   * id
   */
  id?: number;
  /**
   * pageNo
   */
  pageNo: number;
  /**
   * pageSize
   */
  pageSize: number;
  /**
   * 类型：1 ：导入 2 : 导出
   */
  type?: number;
}

/**
 * ExportingRecords
 */
export interface ExportingRecords {
  /**
   * 活动id
   */
  activityId?: number;
  /**
   * 子类型：1 ：ops普通商品  2 :ops ypc商品  3 : sc普通商品 4.Uchoice订单记录, 7.在线表格
   */
  childrenType?: number;
  createTime?: number;
  /**
   * 下载链接
   */
  downloadLink?: string;
  /**
   * 失败code
   */
  failCode?: number;
  /**
   * 失败值
   */
  failCount?: number;
  /**
   * 申请日期
   */
  gmtApplication?: Date;
  id?: number;
  /**
   * 导出id集合
   */
  ids?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 操作人id
   */
  operatorId?: number;
  /**
   * 报告名称
   */
  reportName?: string;
  /**
   * 状态：0-生成中 1-已完成 2-失败
   */
  status?: number;
  /**
   * 成功值
   */
  successCount?: number;
  /**
   * 类型：1 ：导入 2 : 导出
   */
  type?: number;
  updateTime?: number;
}


export const getExportingRecords = (params: GetExportingRecordsParams): Promise<IResponseListProps<ExportingRecords>> => {
  return http.get<GetExportingRecordsParams>('/api-base/export/getExportingRecords', params)
}
