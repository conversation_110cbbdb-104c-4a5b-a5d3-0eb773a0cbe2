/*
 * @Author: <PERSON>
 * @Date: 2024-04-26 10:22:32
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-26 11:05:47
 * @Desc 配置管理相关接口api
 */
import http from '@/services/http'

export interface SaveItemConfigParams {
  /**
   * 价格浮动比例
   */
  defaultPriceFloat?: number
  /**
   * 默认库存
   */
  defaultStock?: number
  /**
   * 是否自动补齐库存
   */
  isAutoReplenish?: boolean
  /**
   * 最低库存
   */
  minStock?: number
  /**
   * 价格浮动提示 eg:0.20
   */
  priceFloatPrompt?: number
}

/**
 * 获取商品配置
 */
export const getItemConfig = () => {
  return http.get<undefined, IResponseProps<API.ChoiceItemConfig>>(
    '/api-mall/aChoice/ops/getItemConfig'
  )
}

/**
 * 保存商品配置
 */
export const saveItemConfig = (params: SaveItemConfigParams) => {
  return http.postJSON<SaveItemConfigParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/saveItemConfig',
    params
  )
}
