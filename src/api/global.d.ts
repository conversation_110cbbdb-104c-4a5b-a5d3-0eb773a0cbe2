interface IBaseSearchConditionProps {
  pageNo: number
  pageSize: number
}

interface IResponseProps<T = any> {
  code: number
  message: string
  result: T
  success: boolean
}

type IResponseListProps<T = any> = IResponseProps<{
  total: number
  list: T[]
}>

type PriceRange = NonNullable<Pick<API.ChoiceProductItem, 'minPrice' | 'maxPrice'>>

type RequiredProps<T, U extends keyof T> = Required<Pick<T, U>> & Omit<T, U>

declare namespace API {
  type TWorkOrderStatus = 0 | 1 | 2
  interface IWorkOrderItemProps {
    commentNums: number
    content: string
    id: number
    isNewMsg: boolean
    pageName: string
    phone: string | null
    registryPhone: string
    sellerId: string
    status: TWorkOrderStatus
    title: string
    type: 0 | 1
    createTime: number
  }

  type TWorkOrderListProps = IResponseListProps<IWorkOrderItemProps>

  type TWorkOrderStatisticsProps = IResponseProps<{
    acceptedNums: number
    finishedNums: number
    pendingNums: number
    processingNums: number
  }>

  interface IWorkOrderCommentProps {
    commentUrls: string[]
    content: string
    id: number
    reviewer: 0 | 1
    sellerId: number
    workOrderId: number
    createTime: number
    /**
     * 是否删除
     */
    isDeleted: 0 | 1
  }

  interface IWorkOrderDetailResultProps extends IWorkOrderItemProps {
    workOrderUrls: string[]
    workOrderComments: IWorkOrderCommentProps[]
  }

  type TWorkOrderDetailProps = IResponseProps<IWorkOrderDetailResultProps>

  interface ChoiceProductItem {
    /**
     * 商品主图
     */
    mainImage?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 类目
     */
    categoryName?: string
    /**
     * 是否已选择 0-未选择 1-已同步 2-同步
     */
    isSelected: number
    /**
     * choice商品Id
     */
    itemId: string
    /**
     * lazada 链接
     */
    lazadaUrl?: string
    /**
     * 限制库存
     */
    limitStock?: number
    /**
     * 最高价格
     */
    maxPrice?: number
    /**
     * 最低价格
     */
    minPrice?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 规格
     */
    spec?: string
    /**
     * 库存（choice）
     */
    stock?: number
    /**
     * 商品名称
     */
    title?: string
    /**
     * 详情页
     */
    description?: string
    /**
     * 同步的错误消息
     */
    errorMsg?: string
  }

  type RemarkData = Required<Pick<API.ChoiceProductItem, 'itemId' | 'remark'>>

  interface ChoiceSku {
    /**
     * 销售属性
     */
    saleProperties?: string[]
    /**
     * sku信息
     */
    skuInfos?: SkuInfo[]
  }

  interface SkuInfo {
    /**
     * sku图
     */
    image?: string
    /**
     * 限制库存
     */
    limitStock?: number
    /**
     * 渠道价
     */
    price?: number
    /**
     * 价格(choice)
     */
    priceChoice?: number
    /**
     * 价格(tiktok)
     */
    priceTiktok?: number
    /**
     * skuId
     */
    skuId: string
    /**
     * sku名称
     */
    skuName?: string
    /**
     * 库存(choice)
     */
    stock?: number

    /**
     * 上下架状态
     */
    status?: boolean
  }

  type TiktokSku = ChoiceSku

  interface TiktokItem {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 类目名称(choice)
     */
    categoryNameChoice?: string
    /**
     * 类目名称(tiktok)
     */
    categoryNameTikTok?: string
    /**
     * 商品Id(choice)
     */
    lazadaItemId?: string
    /**
     * lazada 链接
     */
    lazadaUrl?: string
    /**
     * 最高价格(choice)
     */
    maxPriceChoice?: number
    /**
     * 最高价格(tiktok)
     */
    maxPriceTikTok?: number
    /**
     * 最低价格(choice)
     */
    minPriceChoice?: number
    /**
     * 最低价格(tiktok)
     */
    minPriceTikTok?: number
    /**
     * 商品图片
     */
    producerImage?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 销量(tiktok)
     */
    sales?: number
    /**
     * 店铺名称(tiktok)
     */
    shopName?: string
    /**
     * 状态(tiktok)
     */
    status?: number
    /**
     * 商品Id(tiktok)
     */
    tiktokItemId: string
    /**
     * 商品标题
     */
    title?: string
    /**
     * 详情页
     */
    description?: string

    errorCode?: number

    errorMsg?: string
  }

  interface TikTokShop {
    /**
     * 店铺码
     */
    shopCode: string
    /**
     * 店铺名称
     */
    shopName: string
  }

  type SkuIds = Pick<API.SkuAndStockListItem, 'choiceSkuId' | 'tiktokSkuId'>

  interface SkuAndStockListItem {
    /**
     * 商品Id(choice)
     */
    choiceProductId?: string
    /**
     * skuId(choice)
     */
    choiceSkuId: string
    /**
     * choice总库存
     */
    choiceStock?: number
    /**
     * sku价格(choice)
     */
    choosePrice?: number
    /**
     * 商品名称
     */
    itemName?: string
    /**
     * 限制库存
     */
    limitStock?: number
    /**
     * choice 价格浮动 eg: 0.02 / -0.02
     */
    priceFloat?: number
    /**
     * 店铺名
     */
    shopName?: string
    /**
     * sku图片
     */
    skuImage?: string
    /**
     * sku名称
     */
    skuName?: string
    /**
     * 库存
     */
    stock?: number
    /**
     * sku价格(tiktok)
     */
    tiktokPrice?: number
    /**
     * 商品Id(tiktok)
     */
    tiktokProductId?: string
    /**
     * tiktok销量
     */
    tiktokSales?: number
    /**
     * skuId(tiktok)
     */
    tiktokSkuId?: string
    /**
     * tiktok状态 1-上架 0-下架
     */
    tiktokStatus?: number

    choiceStatus?: number
  }

  interface SkuAndStockRecordItem {
    /**
     * 申请编号
     */
    applicationNumber?: string
    /**
     * 申请数量
     */
    applicationQuantity?: number
    /**
     * 申请状态-true或false
     */
    applicationStatus?: boolean
    /**
     * 申请时间
     */
    applicationTime?: number
    /**
     * 渠道价（choice价格)）
     */
    channelPrice?: number
    /**
     * 商品 Id (choice)
     */
    choiceItemId?: string
    /**
     * sku_id (choice)
     */
    choiceSkuId?: string
    /**
     * 失败原因
     */
    failureReason?: string
    /**
     * 商品名称
     */
    itemName?: string
    /**
     * 操作类型：1-增加库存 2-释放库存
     */
    operationType?: number
    /**
     * 申请人
     */
    operator?: string
    /**
     * 申请前库存
     */
    preApplicationStock?: number
    /**
     * 申请记录 Id
     */
    recordId?: string
    /**
     * sku主图
     */
    skuImage?: string
    /**
     * 规格名称
     */
    skuName?: string
    /**
     * 商品 Id (tiktok)
     */
    tiktokItemId?: string
    /**
     * sku_id (tiktok)
     */
    tiktokSkuId?: string
  }

  interface ChoiceItemConfig {
    /**
     * 价格浮动比例
     */
    defaultPriceFloat?: number
    /**
     * 默认库存
     */
    defaultStock?: number
    /**
     * 是否自动补齐库存
     */
    isAutoReplenish?: boolean
    /**
     * 最低库存
     */
    minStock?: number
    /**
     * 价格浮动提示 eg:0.20
     */
    priceFloatPrompt?: number
  }

  export interface SkuPriceRecord {
    /**
     * 时间
     */
    createTime?: number
    /**
     * 变动后价格
     */
    price?: number
    /**
     * sku类型：1- choice 2-tiktok
     */
    skuType?: number
  }

  export interface OrderListItem {
    /** lazada订单id */
    choiceOrderId?: string
    orderItemList?: OrderItem[]
    /** 包id */
    packageId?: string
    tiktokOrderId?: string
  }

  export interface OrderItem {
    /**
     * 商品取消原因
     */
    cancelReason?: string
    /**
     * 取消时间
     */
    cancelTime?: number
    /**
     * choice订单 Id
     */
    choiceOrderId?: string
    /**
     * 揽收时间
     */
    collectionTime?: number
    /**
     * 创建时间(choice)
     */
    createTimeChoice?: number
    /**
     * 创建时间(tiktok)
     */
    createTimeTiktok?: number
    /**
     * 面单
     */
    docUrl?: string
    /**
     * id
     */
    id?: string
    /**
     * 限制库存
     */
    limitStock?: number
    /**
     * 订单状态 UNPAID: 未支付、AWAITING_SHIPMENT: 等待发货、AWAITING_COLLECTION: 等待揽收、IN_TRANSIT:
     * 运输中、DELIVERED: 已交付、COMPLETED: 已完成、CANCELLED: 已取消、FAILED_TO_COLLECT: 揽收失败
     */
    orderStatus?: string
    /**
     * 订单类型 0-未下单 1-正常订单 2-异常订单
     */
    orderType?: number
    /**
     * 商品 Id
     */
    productId?: string
    /**
     * 商品名称
     */
    productName?: string
    /**
     * 商品数量
     */
    quantity?: number
    /**
     * 发货时间
     */
    rtsTime?: number
    /**
     * 商品售价(choice)
     */
    salePriceChoice?: number
    /**
     * 商品售价(tiktok)
     */
    salePriceTiktok?: number
    /**
     * 卖家折扣金额
     */
    sellerDiscount?: number
    /**
     * 商品的卖方库存编号（SKU）
     */
    sellerSku?: string
    /**
     * 运费
     */
    shippingFee?: number
    /**
     * 商品的运输提供商名称
     */
    shippingProviderName?: string
    /**
     * 店铺名称
     */
    shopName?: string
    /**
     * skuId
     */
    skuId?: string
    /**
     * SKU 图片
     */
    skuImage?: string
    /**
     * SKU 的名称，由产品 SKU 属性（如大小或颜色）组合而成。例如，“黑色，26”。
     */
    skuName?: string
    /**
     * 库存(choice)
     */
    stock?: number
    /**
     * TK订单id
     */
    tiktokOrderId?: string
    /**
     * 总价
     */
    totalAmount?: number
    /**
     * 跟踪号
     */
    trackingNumber?: string

    packageId?: string

    /** 合并单元格 */
    rowSpan?: number
    lazadaCancelStatus?: number
    /**
     * lazada订单状态
     */
    lazadaOrderStatus?: number
    /**
     * 订单错误原因
     */
    lazadaOrderErrorReason?: string
    /**
     * 取消时间 关闭时间 取消失败时间
     */
    lazadaCancelTime?: number
    /**
     * 出库时间
     */
    lazadaOrderDeliverTime?: number
    /**
     * lazada换货订单创建时间
     */
    lazadaOrderReplaceTime?: number
    /**
     * 更换后 sku信息
     */
    changeOrderSkuInfos?: any
    /**
     * 是否已读
     */
    isRead?: boolean
    /**
     * 取消成功时间
     */
    lazadaCancelSuccessTime?: number
    /**
     * 子订单添加的整个订单信息的引用
     */
    listRecord?: any
    /**
     * 替换后的lazadaOrderId
     */
    replacedLazadaOrderId?: string
    /**
     * exchanging
     */
    isReplaceOrder?: boolean
  }

  interface OrderStatusCount {
    statusCounts?: Record<string, number>
  }

  interface SkuStockInfo {
    /**
     * 最大库存数(增加)
     */
    maxStock?: number
    /**
     * 最大库存数(减少)
     */
    maxStockReduce?: number
    /**
     * 商品名称
     */
    productName?: number
    /**
     * sku
     */
    skuName?: string
  }

  interface CategoryData {
    categoryName?: string
    /**
     * 级别
     */
    level?: number
  }

  interface changeSkuItemDto {
    quantity?: number
    tiktokSkuId?: string
  }

  interface ChangeSkuDto {
    changeSkuInfos?: string
    orderId?: string
    orderLineId?: string
  }

  interface GetSkuInfoParams {
    productId?: string
    skuId?: string
  }

  interface GetSkuInfoResponseItem {
    inventory?: number
    sellerSku?: string
    skuId?: string
    skuImage?: string
    skuName?: string
  }

  interface SimilarProductTaskVo {
    /**
     * 被搜文档(文档一)
     */
    beSearchedFile?: string
    /**
     * 创建时间
     */
    createTime?: number
    /**
     * 下载链接
     */
    downloadUrl?: string
    /**
     * 预计完成时间
     */
    expectedCompletionTime?: number
    /**
     * 搜索文档(文档二)）
     */
    searchInfo?: string
    /**
     * 编号
     */
    serialNumber?: number
    /**
     * 状态：0-暂停 1-进行中 2-排队中 3-已完成
     */
    status?: number
    /**
     * 任务 Id
     */
    taskId?: string
  }

  interface TiktokChoiceCategory {
    /**
     * 类目名称
     */
    categoryName?: string
    createTime?: number
    /**
     * 主键Id
     */
    id?: number
    /**
     * 级别
     */
    level?: number
    /**
     * 父级类目
     */
    parentId?: number
  }
}
