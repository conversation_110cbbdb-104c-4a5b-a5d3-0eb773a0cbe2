/*
 * @Author: <PERSON>
 * @Date: 2024-04-26 10:19:09
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-27 15:23:23
 * @Desc 订单管理相关接口api
 */
import http from '@/services/http'

export interface GetOrderListParams {
  productId?: string
  /**
   * lazada订单号
   */
  lazadaOrderNo?: number
  /**
   * 订单状态
   */
  orderStatus?: string
  /**
   * 订单类型 0-未下单 1-正常订单 2-异常订单
   */
  orderType?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺名称
   */
  shopName?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * TK订单号
   */
  tiktokOrderNo?: number
}

export interface GetLazadaOrderListParams {
  productId?: string
  /**
   * lazada订单号
   */
  lazadaOrderNo?: number
  /**
   * 订单状态
   */
  lazadaOrderStatus?: string
  /**
   * 订单类型 0-未下单 1-正常订单 2-异常订单
   */
  orderType?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺名称
   */
  shopName?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * TK订单号
   */
  tiktokOrderNo?: number
}

/**
 * 获取订单列表
 * @param params
 * @returns
 */
export const getOrderList = (params: GetOrderListParams) => {
  return http.get<GetOrderListParams, IResponseListProps<API.OrderListItem>>(
    '/api-mall/aChoice/ops/getOrderList',
    params
  )
}

/**
 * 获取订单状态数量
 * @param params
 * @returns
 */
export const getOrderStatusCount = (params: GetOrderListParams) => {
  return http.get<GetOrderListParams, IResponseProps<API.OrderStatusCount>>(
    '/api-mall/aChoice/ops/getOrderStatusCount',
    params
  )
}

export const getLazadaOrderStatusCount = (params: GetLazadaOrderListParams) => {
  return http.get<GetLazadaOrderListParams, IResponseProps<any>>(
    '/api-mall/aChoice/ops/getLazadaOrderCount',
    params
  )
}

/**
 * 重新下单
 * @param packageId
 * @returns
 */
export const reorder = (packageId: string) => {
  return http.post<{ packageId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/reorder',
    {
      packageId
    }
  )
}

/**
 * 删除订单
 * @param orderId
 * @returns
 */
export const removeOrderRequest = (orderId: string) => {
  return http.post<{ orderId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/cancelOrder',
    {
      orderId
    }
  )
}

/**
 * 订单已读
 */
export const readOrderRequest = (tiktokOrderId: string) => {
  return http.post<{ tiktokOrderId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/read',
    {
      tiktokOrderId
    }
  )
}

/**
 * 更换sku
 * @param orderId
 * @returns
 */
export const changeSkuRequest = (data: API.ChangeSkuDto) => {
  return http.postJSON<API.ChangeSkuDto, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/changeSku',
    data
  )
}

/**
 * 查询可更换 SKU 信息
 */
export const getSkuInfoRequest = (params: API.GetSkuInfoParams) => {
  return http.get<API.GetSkuInfoParams, IResponseProps<API.GetSkuInfoResponseItem[]>>(
    '/api-mall/aChoice/ops/getSkuInfo',
    params
  )
}
