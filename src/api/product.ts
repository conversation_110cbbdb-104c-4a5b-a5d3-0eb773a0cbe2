/*
 * @Author: <PERSON>
 * @Date: 2024-04-24 10:24:00
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-24 11:48:00
 * @Desc 商品管理相关接口api
 */
import http from '@/services/http'
import type { RcFile } from 'antd/lib/upload'
import qs from 'qs'

export interface GetChoiceItemListParams extends IBaseSearchConditionProps {
  /**
   * 类目名称
   */
  categoryName?: string
  /**
   * 是否已选择
   */
  isSelected?: boolean
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 商品价格(最低)
   */
  minPrice?: number
  /**
   * 商品价格(最高)
   */
  maxPrice?: number
  /**
   * 商品图片搜索
   */
  productImage?: string
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * lazada库存(最低)
   */
  minLazadaStock?: number
  /**
   * lazad<PERSON>库存(最高)
   */
  maxLazadaStock?: number
}

export interface SetRemarkParams {
  itemId: string
  itemType: 1 | 2
  remark: string
}

export interface TiktokItemListParams extends IBaseSearchConditionProps {
  /**
   * 类目名称
   */
  categoryName?: string
  /**
   * 商品id
   */
  itemId?: number
  /**
   * 商品价格(最高)
   */
  maxPrice?: number
  /**
   * 商品价格(最低)
   */
  minPrice?: number
  /**
   * 商品图片搜索
   */
  productImage?: string
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺码
   */
  shopCode?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 上架状态
   */
  status?: number
  /**
   * tiktok库存(最低)
   */
  minTiktokStock?: number
  /**
   * tiktok库存(最高)
   */
  maxTiktokStock?: number
  /**
   * lazada库存(最低)
   */
  minLazadaStock?: number
  /**
   * lazada库存(最高)
   */
  maxLazadaStock?: number
}

export interface GetSkuAndStockListParams extends IBaseSearchConditionProps {
  /**
   * choice商品状态 1-上架 0-下架
   */
  choiceStatus?: number
  /**
   * 图片搜索
   */
  imageSearch?: string
  /**
   * 商品名称
   */
  itemName?: string
  /**
   * 最大价格
   */
  maxPrice?: number
  /**
   * 最大价格浮动区间
   */
  maxPriceRange?: number
  /**
   * 最小价格
   */
  minPrice?: number
  /**
   * 最小价格浮动区间
   */
  minPriceRange?: number
  /**
   * 店铺编码
   */
  shopCode?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * tiktok商品状态 1-上架 0-下架
   */
  tiktokStatus?: number
  itemId?: string
  skuId?: string
  [field: string]: any
}

export interface ApplyStockParams {
  /**
   * skuId(choice)
   */
  choiceSkuId: string
  /**
   * 锁定库存数量
   */
  lockStock: number
  /**
   * 操作类型：1-增加 2-减少
   */
  operateType: number
}

export interface GetSkuAndStockRecordParams extends IBaseSearchConditionProps {
  /**
   * 商品Id(choice)
   */
  choiceItemId?: number
  /**
   * 图片搜索
   */
  imageSearch?: string
  /**
   * 商品名称
   */
  itemName?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 申请状态：1-成功 0-失败
   */
  status?: number
  /**
   * 商品Id(tiktok)
   */
  tiktokItemId?: number
  skuId?: string
}

export type ModifyProductPricesParams = {
  price: number
  skuId: string
}[]

export interface SetSelectedParams {
  /**
   * 商品id
   */
  choiceSkuList: {
    /**
     * tiktok价格
     */
    applyPrice: number
    /**
     * 申请库存
     */
    applyStock: number
    /**
     * skuId
     */
    skuId: string
  }[]
  /**
   * 店铺码
   */
  shopCode: string
  description?: string
  itemId?: string
}

export interface getSkuPriceRecordListParams {
  /**
   * choiceSkuId
   */
  choiceSkuId?: string
  /**
   * 类型 1-choice 2-tiktok
   */
  skuType: number
  /**
   * tiktokSkuId
   */
  tiktokSkuId?: string
}

export interface SetItemStatusParms {
  itemId: string
  /** 状态 1-上架 0-下架 */
  status: number
}

export interface UploadSizeChartParams {
  tiktokItemId: string
  formData: FormData
}

export interface SyncShopItemParams {
  /**
   * 商品描述
   */
  itemDesc?: string
  /**
   * 商品名称
   */
  itemName?: string
  /**
   * tiktokItemId
   */
  tiktokItemId: string
}

export interface GetSimilarItemTaskListParams extends IBaseSearchConditionProps {
  /**
   * 创建时间开始
   */
  createTimeBegin?: number
  /**
   * 创建时间结束
   */
  createTimeEnd?: number
  /**
   * 编号
   */
  serialNumber?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 状态：0-暂停 1-进行中 2-排队中 3-已完成
   */
  status?: number
}

export interface AddMatchTaskParams {
  /**
   * 文档一
   */
  beSearchedFilter?: string
  /**
   * 被搜类型：1-lazada商品库 2-上传文档
   */
  beSearchedType?: number
  /**
   * 搜索文档(类型为 1 时，存的类目名)
   */
  searchFilter?: string
  /**
   * 搜索类型：1-lazada商品库 2-上传文档
   */
  searchType?: number

  beSearchedFile?: RcFile
  searchedFile?: RcFile
}

export interface SetSampleCostParams {
  /**
   * 商品id
   */
  productId: number
  /**
   * 寄样成本
   */
  sampleCost: number
}

export interface SetProductListParams {
  pageNo: number
  pageSize: number
  param: string
  sampleCost: 0 | 1 | 2
  sortBy: string
  sortDirection: 'asc' | 'desc'
}

/**
 *  设置寄样成本
 */
export const setSampleCost = (params: SetSampleCostParams) => {
  return http.postJSON('/api-uchoice/merchant/setSampleCost', params)
}

/**
 *  获取商品列表
 */
export const getProductList = (params: SetProductListParams) => {
  return http.get('/api-uchoice/merchant/productList', params)
}

//获取商品列表(demo)
export const productListMock = (params: SetProductListParams) => {
  return http.get('/api-uchoice/merchant/productListMock', params)
}

/**
 * 设置备注
 * @param params
 * @param params.itemType 1:choice  2:tiktok
 * @returns
 */
export const setRemark = (params: SetRemarkParams) => {
  return http.postJSON<SetRemarkParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/setRemark',
    params
  )
}

/**
 * 我的商品-Tiktok商品列表
 */
export const getTiktokItemList = (params: TiktokItemListParams) => {
  return http.get<TiktokItemListParams, IResponseListProps<API.TiktokItem>>(
    '/api-mall/aChoice/ops/getTiktokItemList',
    params
  )
}

/**
 * 获取店铺列表
 */
export const getShopList = () => {
  return http.get<any, IResponseProps<API.TikTokShop[]>>('/api-mall/aChoice/ops/getShopList')
}

/**
 * 取消选择
 */
export const cancelSelected = (itemId: string) => {
  return http.post<{ itemId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/cancelSelected',
    { itemId }
  )
}

/**
 * 获取sku库存列表
 */
export const getSkuAndStockList = (params: GetSkuAndStockListParams) => {
  return http.get<GetSkuAndStockListParams, IResponseListProps<API.SkuAndStockListItem>>(
    '/api-mall/aChoice/ops/getSkuAndStockList',
    params
  )
}

/**
 * 释放库存
 */
export const releaseStock = (choiceSkuId: string) => {
  return http.post<{ choiceSkuId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/releaseStock',
    { choiceSkuId }
  )
}

/**
 * 申请库存
 */
export const applyStock = (params: ApplyStockParams) => {
  return http.postJSON<ApplyStockParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/applyStock',
    params
  )
}

/**
 * 获取库存申请记录
 */
export const getSkuAndStockRecord = (params: GetSkuAndStockRecordParams) => {
  return http.get<GetSkuAndStockRecordParams, IResponseListProps<API.SkuAndStockRecordItem>>(
    '/api-mall/aChoice/ops/getSkuAndStockRecord',
    params
  )
}

/**
 * 重新发起申请
 */
export const reissueApplication = (recordId: string) => {
  return http.post<{ recordId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/reissueApplication',
    { recordId }
  )
}

/**
 * 修改商品价格
 */
export const modifyProductPrices = (params: ModifyProductPricesParams) => {
  return http.postJSON<ModifyProductPricesParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/batchModifyProductPrices',
    params
  )
}

/**
 * 选择商品到店铺
 */
export const setSelected = (params: SetSelectedParams) => {
  return http.postJSON<SetSelectedParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/setSelected',
    params
  )
}

/**
 * 获取Tiktok商品sku列表
 */
export const getTiktokSkuList = (itemId: string) => {
  return http.get<{ itemId: string }, IResponseProps<API.TiktokSku>>(
    '/api-mall/aChoice/ops/getTiktokSkuList',
    { itemId }
  )
}

/**
 * 获取sku价格变动记录
 */
export const getSkuPriceRecordList = (params: getSkuPriceRecordListParams) => {
  return http.get<getSkuPriceRecordListParams, IResponseProps<API.SkuPriceRecord[]>>(
    '/api-mall/aChoice/ops/getSkuPriceRecordList',
    params
  )
}

/**
 * 获取超过浮动标识的 sku 列表
 */
export const getOverStockList = (params: GetSkuAndStockListParams) => {
  return http.get<GetSkuAndStockListParams, IResponseProps<number>>(
    '/api-mall/aChoice/ops/getOverStockList',
    params
  )
}

/**
 * 获取库存申请失败数量
 */
export const getSkuAndStockRecordCount = (params: GetSkuAndStockRecordParams) => {
  return http.get<GetSkuAndStockRecordParams, IResponseProps<number>>(
    '/api-mall/aChoice/ops/getSkuAndStockRecordCount',
    params
  )
}

/**
 * 获取库存信息
 */
export const getSkuStockInfo = (tiktokSkuId: string) => {
  return http.get<{ tiktokSkuId: string }, API.SkuStockInfo>(
    '/api-mall/aChoice/ops/getSkuStockInfo',
    { tiktokSkuId }
  )
}

/**
 * 上下架商品
 * @param params
 * @returns
 */
export const setItemStatus = (params: SetItemStatusParms) => {
  return http.post<SetItemStatusParms, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/setItemStatus',
    params
  )
}
/**
 *
 * @param choiceItemId
 * @returns
 */
export const getChoiceItemCategory = (choiceItemId: string) => {
  return http.get<{ choiceItemId: string }, IResponseProps<API.CategoryData[]>>(
    '/api-mall/aChoice/ops/getChoiceItemCategory',
    {
      choiceItemId
    }
  )
}

/**
 * 上传尺寸图
 * @param paams
 * @returns
 */
export const uploadSizeChart = (paams: UploadSizeChartParams) => {
  return http.upload<FormData, IResponseProps<boolean>>(
    `/api-mall/aChoice/ops/uploadSizeChart?tiktokItemId=${paams.tiktokItemId}`,
    paams.formData
  )
}

/**
 * 发起同步商品
 * @param paams
 * @returns
 */
export const syncShopItem = (paams: SyncShopItemParams) => {
  return http.postJSON<SyncShopItemParams, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/syncShopItem',
    paams
  )
}

/**
 * 发布商品
 * @param params
 * @returns
 */
export const publish = (tiktokItemId: string) => {
  return http.post<{ tiktokItemId: string }, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/publish',
    { tiktokItemId }
  )
}

/**
 * 导出商品库
 */
export const exportChoiceItem = (params: any) => {
  return http.get<any, IResponseProps<boolean>>('/api-mall/aChoice/ops/exportChoiceItem', params)
}

/**
 * 导出我的商品
 */
export const exportTiktokItem = (params: any) => {
  return http.get<any, IResponseProps<boolean>>('/api-mall/aChoice/ops/exportTiktokItem', params)
}

/**
 * 获取相似商品任务列表
 */
export const getSimilarItemTaskList = (params: GetSimilarItemTaskListParams) => {
  return http.get<GetSkuAndStockRecordParams, IResponseListProps<API.SimilarProductTaskVo>>(
    '/api-mall/aChoice/ops/getSimilarItemTaskList',
    params
  )
}

/**
 * 获取choice一级类目
 */
export const getChoiceCategory = () => {
  return http.get<SetItemStatusParms, IResponseProps<API.TiktokChoiceCategory[]>>(
    '/api-mall/aChoice/ops/getChoiceCategory'
  )
}

/**
 * 新增匹配任务
 */
export const addMatchTask = (params: AddMatchTaskParams) => {
  const formData = new FormData()

  if (params.beSearchedFile) {
    formData.append('beSearchedFile', params.beSearchedFile as RcFile)
  }

  if (params.searchedFile) {
    formData.append('searchedFile', params.searchedFile as RcFile)
  }

  delete params.beSearchedFile
  delete params.searchedFile

  return http.upload<FormData, IResponseProps<boolean>>(
    '/api-mall/aChoice/ops/addMatchTask?' + qs.stringify(params),
    formData
  )
}

export const getYoupikOperateProducts = (params: any) => {
  return http.get<any, IResponseListProps<any>>(
    '/api-uchoice/merchant/shop/youpik/item/list',
    params,
  )
}

// 编辑商品机审状态
export const auditYoupikOperateProduct = (id: string, status: string) => {
  return http.postJSON<any, IResponseProps<any>>(
    `/api-uchoice/merchant/shop/youpik/item/edit/audit?id=${id}&status=${status}`,
  )
}
