import http, { cancelRequest } from '@/services/http'

export interface ReviewParams {
  isSeller?: boolean
  orderNo?: number
  otherReason?: string
  reasonCode?: string
  type?: number
}
export interface anchorsOrdersParams {
  /**
   * 最低成交件数
   */
  minDeal?: number
  /**
   * 最低粉丝量
   */
  minFans?: number
  /**
   * 最低履约率
   */
  minPerformance?: number
  /**
   * 达人昵称
   */
  nickName?: string
  /**
   * 订单号
   */
  orderNo?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 商品 Id
   */
  productId?: number
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺编码
   */
  shopCode?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 订单状态:全部：0,待审核：2000, 待发货：2001, 已发货：3000, 已完成：4000, 已取消：-2000
   */
  status?: number
}
export interface ordersItemsParams {
  memberId: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 订单状态
   */
  status?: string
}

export interface exportOrdersParams {
  /**
   * 最低成交件数
   */
  minDeal?: number
  /**
   * 最低粉丝量
   */
  minFans?: number
  /**
   * 最低履约率
   */
  minPerformance?: number
  /**
   * 达人昵称
   */
  nickName?: string
  /**
   * 订单号
   */
  orderNo?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 商品 Id
   */
  productId?: number
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺编码
   */
  shopCode?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 订单状态:全部：0,待审核：2000, 待发货：2001, 已发货：3000, 已完成：4000, 已取消：-2000
   */
  status?: string
}
/**
 * 订单状态
 */
export enum Status {
  Status2000 = '-2000',
  The0 = '0',
  The2000 = '2000',
  The2001 = '2001',
  The3000 = '3000',
  The4000 = '4000'
}

export interface OrdersStatsParams {
  /**
   * 最低成交件数
   */
  minDeal?: number
  /**
   * 最低粉丝量
   */
  minFans?: number
  /**
   * 最低履约率
   */
  minPerformance?: number
  /**
   * 达人昵称
   */
  nickName?: string
  /**
   * 订单号
   */
  orderNo?: number
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 商品 Id
   */
  productId?: number
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 店铺编码
   */
  shopCode?: string
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 订单状态:全部：0,待审核：2000, 待发货：2001, 已发货：3000, 已完成：4000, 已取消：-2000
   */
  status?: number
  noShowError?: boolean
}
export interface OrderNoDeliver {
  orderNo?: string
  courierCompany?: string
  trackingNumber?: string
}
export interface OrdersFulfillmentsParams {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 数量
   */
  pageSize?: number
  /**
   * 排序字段
   */
  sortBy?: string
  /**
   * 排序值 asc/desc
   */
  sortDirection?: string
  /**
   * 履约类型
   */
  type: number
}
//分页查询达人订单
const requestKey = 'anchorsOrdersKey'
export const anchorsOrders = (params: anchorsOrdersParams) => {
  return http.get<anchorsOrdersParams, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/anchors/orders',
    params,
    undefined,
    undefined,
    requestKey
  )
}
//查询指定达人的订单列表

export const ordersItems = (params: ordersItemsParams) => {
  return http.get<ordersItemsParams, IResponseProps<any>>(
    `/api-uchoice/merchant/sample/anchors/${params.memberId}/orders`,
    params,
    undefined,
    undefined,
    `ordersItems_${params.memberId}`
  )
}
//获取物流信息
export const getExpressDeliveryInfo = (params: any) => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/order/getExpressDeliveryInfo', params)
}
// 发货物流
export const ttLogistics = (): any => {
  return http.post<any>('/api-uchoice/statistics/ttLogistics')
}
//导出订单
export const exportOrders = (params: exportOrdersParams) => {
  return http.get<exportOrdersParams, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/export-orders',
    params
  )
}

//导入运单号
export const importTrackingNumbers = (file: any) => {
  return http.upload<any, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/import-tracking-numbers',
    file
  )
}

//获取拒绝理由列表
export const rejectionReasons = () => {
  return http.get<any>('/api-uchoice/merchant/sample/rejection-reasons')
}

//批量审核订单
export const batchReview = (orderNos: any[]) => {
  return http.postJSON<any[], IResponseProps<any>>(
    '/api-uchoice/merchant/sample/suppliers/orders/batch-review',

    orderNos
  )
}

//查询订单统计信息

export const ordersStats = (params: OrdersStatsParams) => {
  const noShowError = params?.noShowError ? params?.noShowError : true
  if (params?.noShowError || params?.noShowError == false) {
    delete params.noShowError
  }
  return http.get<OrdersStatsParams, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/suppliers/orders/stats',
    params,
    undefined,
    null,
    undefined,
    noShowError
  )
}

//订单发货
export const orderNodeliver = (params: OrderNoDeliver) => {
  return http.postJSON<OrderNoDeliver, IResponseProps<any>>(
    `/api-uchoice/merchant/sample/suppliers/orders/${params.orderNo}/deliver`,
    params
  )
}
//增补发货操作
export const supplementShippingOperation = (params: OrderNoDeliver) => {
  return http.postJSON<OrderNoDeliver, IResponseProps<any>>(
    '/api-uchoice/order/supplementShippingOperation',
    params
  )
}
//订单审核
export const orderNoReview = (params: ReviewParams) => {
  return http.postJSON<ReviewParams, IResponseProps<any>>(
    `/api-uchoice/merchant/sample/suppliers/orders/${params.orderNo}/review`,
    params
  )
}

//查询商家店铺列表
export const suppliersShops = (params?: any) => {
  return http.get<any, IResponseProps<any>>('/api-uchoice/merchant/sample/suppliers/shops', params)
}

//获取履约类型数量统计
export const fulfillmentCounts = (orderNo: string) => {
  return http.get<string, IResponseProps<any>>(
    `/api-uchoice/merchant/sample/suppliers/orders/${orderNo}/fulfillment-counts`
  )
}

//分页查询履约内容
export const ordersFulfillments = (params: OrdersFulfillmentsParams) => {
  return http.get<OrdersFulfillmentsParams, IResponseProps<any>>(
    '/api-uchoice/merchant/sample/suppliers/orders/fulfillments',
    params
  )
}
//检查是否有新消息

export const hasNewMessages = () => {
  return http.get<null, IResponseProps<any>>('/api-uchoice/merchant/sample/has-new-messages')
}

//标记消息为已读
export const markMessagesRead = () => {
  return http.post<null, IResponseProps<any>>('/api-uchoice/merchant/sample/mark-messages-read')
}
