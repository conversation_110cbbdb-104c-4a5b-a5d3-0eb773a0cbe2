import http from '../services/http'

export default {
  getSupplierShops: (params: any): any => {
    return http.get<any>('/api-uchoice/merchant/sample/suppliers/shops', params)
  },
  getAuthedTiktokAccountList: (params: any): any => {
    return http.get<any>('/api-uchoice/merchant/shop/tiktok/auth/list', params)
  },
  getAutoReviewList: (params: any): any => {
    return http.get<any>('/api-uchoice/merchant/shop/machine-audit/config', params)
  },
  updateAutoReview: (data: any): any => {
    return http.post<any>('/api-uchoice/merchant/shop/machine-audit/conditions', data, {
      'Content-Type': 'application/json'
    })
  },
  getTiktokAuthUrl: (route: any): any => {
    return http.get<any>('/api-uchoice/merchant/shop/tiktok/auth/url', {
      route,
    })
  },
}
