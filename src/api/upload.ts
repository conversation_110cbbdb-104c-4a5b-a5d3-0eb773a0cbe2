import http from '../services/http'

export const uploadImage = (formData: any) => {
  return http.upload<any, IResponseProps<string>>('/api-base/upload/uploadFile', formData)
}

/**
 * 上传achoice文件
 * @param formData
 * @returns
 */
export const uploadAchoiceFile = (formData: FormData) => {
  return http.upload<FormData, IResponseProps<string>>(
    '/api-base/upload/uploadAchoiceFile',
    formData
  )
}
