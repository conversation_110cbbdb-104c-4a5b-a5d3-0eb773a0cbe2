import http from '../services/http'

export interface IUchoiceMemberByPageParamsProps extends Partial<IBaseSearchConditionProps> {
  sortDirection: string
}

export interface IOatuthTokenParamsProps {
  username: string
  grantType: string
  password: string
  cloudflareToken: string
  accountType: string
  nation?: string
}

export default {
  getUchoiceMemberByPage: (params: IUchoiceMemberByPageParamsProps): any => {
    return http.get<IUchoiceMemberByPageParamsProps>(
      '/api-uchoice/uChoice/member/getUchoiceMemberByPage',
      params
    )
  },
  oatuthToken: (data: IOatuthTokenParamsProps): any => {
    return http.post<IOatuthTokenParamsProps>('/auth/authenticate', data, {
      'Content-Type': 'application/json'
    })
  },
  usersCurrent: (): any => {
    return http.get<any>('/member/ops/info')
  },
  syncSuppliers: (site: string): any => {
    return http.post<any>(`/api-uchoice/merchant/shop/suppliers/sync?site=${site}`)
  },
}
