import React, { useRef, useState } from 'react'
import { Avatar } from 'antd'
import defaultAvatar from '../../assets/default_avatar.png'

interface CustomizeAvatarProps {
  avatar?: string
}

const CustomizeAvatar: React.FC<CustomizeAvatarProps> = ({ avatar = defaultAvatar }) => {
  const [hasError, setHasError] = useState(false)

  const handleImageError = () => {
    setHasError(true)
    return true // 图片加载失败时更新状态
  }

  return (
    <Avatar
      src={hasError ? defaultAvatar : avatar ? avatar : defaultAvatar}
      size={40}
      onError={handleImageError}
    />
  )
}

export default CustomizeAvatar
