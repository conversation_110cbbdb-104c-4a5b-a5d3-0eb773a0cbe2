/*
 * @Author: <PERSON>
 * @Date: 2024-05-11 10:36:12
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-27 11:57:55
 * @Desc 头像
 */
import { useBoolean } from 'ahooks'
import { Image, Spin } from 'antd'
import { useEffect, useMemo, useState, type CSSProperties } from 'react'

interface Props {
  src?: string
  width?: string | number
  height?: string | number
}

const AvatarImageStyle: CSSProperties = {
  objectFit: 'contain',
  backgroundColor: 'transparent'
}

const SpinStyle: CSSProperties = {
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}

const AvatarImage = (props: Props) => {
  const { width = 50, height = 50 } = props
  const [srcArr, setSrcArr] = useState<string[]>([])
  const [previewVisible, { toggle: previewVisibleToggle }] = useBoolean()

  useEffect(() => {
    const src = props?.src || ''
    if (src.length > 0) {
      const srcArr = src?.split(',')
      setSrcArr(srcArr)
    }
  }, [props?.src])

  const srcMap = useMemo(() => {
    return srcArr.slice(1)
  }, [srcArr])

  return (
    <Image.PreviewGroup
      preview={{
        visible: previewVisible,
        onVisibleChange: previewVisibleToggle
      }}
    >
      <Image
        src={srcArr[0]}
        width={width}
        height={height}
        placeholder={<Spin spinning style={SpinStyle} />}
        style={{ ...AvatarImageStyle, minWidth: width, borderRadius: '50%' }}
        loading="lazy"
      />
      {previewVisible && srcMap.map((src) => <Image key={src} src={src} hidden />)}
    </Image.PreviewGroup>
  )
}

export default AvatarImage
