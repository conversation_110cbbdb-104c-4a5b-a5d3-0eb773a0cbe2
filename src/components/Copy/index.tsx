import { intlTrans } from '@/utils/common'
import { copy } from '@/utils/copy'
import { CopyTwoTone } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { useCallback } from 'react'

interface CopyProps {
  text: string
  style?: React.CSSProperties
  tip?: string
  IconColor?: string
}

const Copy = (props: CopyProps) => {
  const { style, tip = intlTrans('common.Copy'), IconColor } = props

  const handleClick = useCallback(() => {
    copy(props.text)
  }, [props.text])

  return (
    <Tooltip title={tip}>
      <CopyTwoTone
        twoToneColor={IconColor ? IconColor : '#009995'}
        onClick={handleClick}
        style={{ marginLeft: 5, ...style }}
      />
    </Tooltip>
  )
}

export default Copy
