import dataIcon from '@/assets/images/icons/icon_data.png'
import dataIconActive from '@/assets/images/icons/icon_data_active.png'
import productIcon from '@/assets/images/icons/icon_product.png'
import productIconActive from '@/assets/images/icons/icon_product_active.png'
import accountIcon from '@/assets/images/icons/icon_account.png'
import accountActiveIcon from '@/assets/images/icons/icon_account_active.png'
import machineSetIcon from '@/assets/images/icons/icon_machine_set.png'
import machineSetIconActive from '@/assets/images/icons/icon_machine_set_active.png'
import sampleApplyIcon from '@/assets/images/icons/icon_sample_apply.png'
import sampleApplyIconActive from '@/assets/images/icons/icon_sample_apply_active.png'
import { useEffect, useMemo, useState } from 'react'
import { history, useModel } from 'umi'
import styles from './index.less'
import { isAuthShopStorage, isToAuditCountStorage } from '@/utils/storage'

// 图标配置
const ICONS: any = {
  '/dataAnalytics': {
    icon: dataIcon,
    activeIcon: dataIconActive
  },
  '/sampleApply': {
    icon: sampleApplyIcon,
    activeIcon: sampleApplyIconActive
  },
  '/commodity': {
    icon: productIcon,
    activeIcon: productIconActive
  },
  '/machineReview': {
    icon: machineSetIcon,
    activeIcon: machineSetIconActive
  },
  '/accountManage': {
    icon: accountIcon,
    activeIcon: accountActiveIcon
  }
}

const CustomMenuItem = ({ item, defaultDom, menuProps }: any) => {
  const { initialState } = useModel('@@initialState')
  const { path, pro_layout_parentKeys, routes } = item
  const { matchMenuKeys } = menuProps
  const active = matchMenuKeys[0] === path

  const [showLockIcon, setShowLockIcon] = useState(false)
  const [reviewRedIcon, setReviewRedIcon] = useState(false)
  const icon = useMemo(() => {
    const icon = ICONS[path]
    return icon ? icon[active ? 'activeIcon' : 'icon'] : ''
  }, [active, path])
  // isToAuditCounStorage
  useEffect(() => {
    const interval = setInterval(() => {
      isAuthShopStorage.get() === 'true' ? setShowLockIcon(false) : setShowLockIcon(true)
      isToAuditCountStorage.get() === 'true' ? setReviewRedIcon(true) : setReviewRedIcon(false)
    }, 500)

    return () => {
      clearInterval(interval)
    }
  }, [])

  return pro_layout_parentKeys.length === 0 && icon ? (
    <div
      onClick={() => {
        if (!routes || routes.length === 0) {
          history.push(path)
        }
      }}
      className={styles.menu_item_container}
    >
      <img
        className={styles.icon}
        src={icon}
        style={{ marginRight: initialState?.settings.collapsed ? 24 : 10 }}
      />
      {defaultDom} {reviewRedIcon && path == '/sampleApply' && <div className={styles.reviewRed} />}
    </div>
  ) : (
    <div
      onClick={() => {
        history.push(path)
      }}
    >
      {defaultDom}
      {showLockIcon &&
        (path == '/dataAnalytics/videoPerformance' || path == '/commodity/manage') &&
        '🔐'}
    </div>
  )
}

export default CustomMenuItem
