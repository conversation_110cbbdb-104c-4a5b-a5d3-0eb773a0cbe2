import { useEffect, useState } from 'react'
import { Button, Table, Modal } from 'antd'
import { formatTime } from '@/utils/format'
import { getExportingRecords } from '@/api/base'
import { intlTrans } from '@/utils/common'

interface Props {
  open: boolean
  onCancel: () => void
  childrenType: number
}

export const DownloadRecordsModal = ({ open, onCancel, childrenType }: Props) => {
  const [data, setData] = useState<any>([])
  //分页
  const [pageNo, setPageNo] = useState(1)
  const [pageToal, setPageTotal] = useState(1)
  //每页条数
  const [pageSize, setPageSize] = useState(10)
  const [loading, setLoading] = useState<boolean>(false)

  // 表格记录
  const handleExportingRecords = async (pageNo: any, pageSize: any) => {
    setLoading(true)
    const params = {
      pageSize: pageSize,
      pageNo: pageNo,
      type: 2,
      childrenType
    }
    const res: any = await getExportingRecords(params)
    if (res && res.code === 200 && res.result) {
      setData(res.result.list)
      setLoading(false)
      setPageTotal(res.result.total)
    } else {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      handleExportingRecords(pageNo, pageSize)
    }
  }, [open])

  const ExportHistory = () => {
    // 状态
    const getStatus = (status: any) => {
      // eslint-disable-next-line default-case
      switch (status) {
        case 0:
          return 'loading'
        case 1:
          return intlTrans('common.Completed')
        case 2:
          return intlTrans('common.Failure')
      }
    }
    const columns: any = [
      //   用户名称
      {
        title: intlTrans('common.UserName'),
        dataIndex: 'operator',
        key: 'operator',
        align: 'center'
      },
      //   时间
      {
        title: intlTrans('common.time'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return <span> {formatTime(record.gmtApplication)}</span>
        }
      },
      //   文件名
      {
        title: intlTrans('common.fileName'),
        dataIndex: 'reportName',
        key: 'reportName',
        align: 'center'
      },
      //   状态
      {
        title: intlTrans('common.Status'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return (
            <span style={{ color: record.status == 2 ? 'red' : '' }}>
              {' '}
              {getStatus(record.status)}
            </span>
          )
        }
      },
      //   操作
      {
        title: intlTrans('common.Operation'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return record.status == 1 ? (
            <a href={record.downloadLink} download={record.reportName + '.xlsx'}>
              {intlTrans('common.download')}
            </a>
          ) : (
            <span style={{ color: '#999999' }}>{intlTrans('common.download')}</span>
          )
        }
      }
    ]

    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="primary"
            onClick={() => handleExportingRecords(pageNo, pageSize)}
            style={{ marginBottom: 10 }}
          >
            {intlTrans('common.refresh')}
          </Button>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          rowKey={'id'}
          loading={loading}
          pagination={{
            current: pageNo,
            total: pageToal,
            pageSize: pageSize,
            showTotal(total, range) {
              return `${intlTrans('common.Total')} ${total}`
            },
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => {
              setPageNo(page)
              setPageSize(pageSize)
              handleExportingRecords(page, pageSize)
            }
          }}
          scroll={{
            x: 1000,
            y: 500
          }}
        />
      </>
    )
  }
  //
  return (
    <>
      <Modal
        width={1000}
        footer={[]}
        title={intlTrans('common.exportHistory')}
        visible={open}
        destroyOnClose
        onCancel={() => {
          onCancel()
          setPageNo(1)
          setPageSize(10)
        }}
      >
        <ExportHistory />
      </Modal>
    </>
  )
}
