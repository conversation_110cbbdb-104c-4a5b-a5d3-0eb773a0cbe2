import { useEffect, useRef, useState } from 'react'
import { Button, Table, Modal, Tabs } from 'antd'
import { formatTime } from '@/utils/format'
import { getExportingRecords } from '@/api/base'
import { intlTrans } from '@/utils/common'
import styles from './index.less'

interface Props {
  open: boolean
  onCancel: () => void
}

export const DownloadTabRecordsModal = ({ open, onCancel }: Props) => {
  const [data, setData] = useState<any>([])
  const [pageNo, setPageNo] = useState(1)
  const [pageToal, setPageTotal] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [loading, setLoading] = useState<boolean>(false)
  const typeRef = useRef('1')
  const childrenTypeRef = useRef('23')

  const handleExportingRecords = async (pageNo: any, pageSize: any) => {
    setLoading(true)
    const params: any = {
      pageSize: pageSize,
      pageNo: pageNo,
      type: typeRef.current,
      childrenType: childrenTypeRef.current
    }
    const res: any = await getExportingRecords(params)
    if (res && res.code === 200 && res.result) {
      setData(res.result.list)
      setLoading(false)
      setPageTotal(res.result.total)
    } else {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      handleExportingRecords(pageNo, pageSize)
    }
  }, [open])

  const ExportHistory = () => {
    // 状态
    const getStatus = (status: any) => {
      // eslint-disable-next-line default-case
      switch (status) {
        case 0:
          return 'loading'
        case 1:
          return intlTrans('common.Completed')
        case 2:
          return intlTrans('common.Failure')
      }
    }
    const columns: any = [
      //   用户名称
      {
        title: intlTrans('common.UserName'),
        dataIndex: 'operator',
        key: 'operator',
        align: 'center'
      },
      //   时间
      {
        title: intlTrans('common.time'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return <span> {formatTime(record.gmtApplication)}</span>
        }
      },
      //   文件名
      {
        title: intlTrans('common.fileName'),
        dataIndex: 'reportName',
        key: 'reportName',
        align: 'center'
      },
      //   状态
      {
        title: intlTrans('common.Status'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return (
            <>
              <div style={{ color: record.status == 2 ? 'red' : '' }}>
                {' '}
                {getStatus(record.status)}
              </div>
              {(record.successCount || record.successCount === 0) &&
                (record.failCount || record.failCount === 0) && (
                  <>
                    <div className={styles.num_label}>
                      <div>
                        ({intlTrans('common.成功数量')}:
                        <span style={{ color: 'green' }}> {record.successCount}</span>, &nbsp;
                      </div>
                      <div>
                        {intlTrans('common.失败数量')}:
                        <span style={{ color: 'red' }}>{record.failCount})</span>
                      </div>
                    </div>
                    <div className={styles.show_label}>{intlTrans('common.请下载查看')}</div>
                  </>
                )}
            </>
          )
        }
      },
      //   操作
      {
        title: intlTrans('common.Operation'),
        dataIndex: 'id',
        key: 'id',
        align: 'center',
        render: (text: any, record: any, index: any) => {
          return record.status == 1 ? (
            <a href={record.downloadLink} download={record.reportName + '.xlsx'}>
              {intlTrans('common.download')}
            </a>
          ) : (
            <span style={{ color: '#999999' }}>{intlTrans('common.download')}</span>
          )
        }
      }
    ]

    return (
      <>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="primary"
            onClick={() => handleExportingRecords(pageNo, pageSize)}
            style={{ marginBottom: 10 }}
          >
            {intlTrans('common.refresh')}
          </Button>
        </div>
        <Table
          columns={columns}
          dataSource={data}
          rowKey={'id'}
          loading={loading}
          pagination={{
            current: pageNo,
            total: pageToal,
            pageSize: pageSize,
            showTotal(total, range) {
              return `${intlTrans('common.Total')} ${total}`
            },
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => {
              setPageNo(page)
              setPageSize(pageSize)
              handleExportingRecords(page, pageSize)
            }
          }}
          scroll={{
            x: 1000,
            y: 500
          }}
        />
      </>
    )
  }
  const tabArr = [
    { label: intlTrans('common.导入历史'), value: '23', key: '23' },

    { label: intlTrans('common.exportHistory'), value: '22', key: '22' }
  ]
  const onTabChange = (e: string) => {
    childrenTypeRef.current = e
    if (e == '23') {
      typeRef.current = '1'
    } else {
      typeRef.current = '2'
    }
    setPageNo(1)
    setPageSize(10)
    handleExportingRecords(1, 10)
  }
  return (
    <>
      {open && (
        <Modal
          width={1000}
          footer={[]}
          title={intlTrans('common.历史记录')}
          open={open}
          destroyOnClose
          onCancel={() => {
            onCancel()
            setPageNo(1)
            setPageSize(10)
            childrenTypeRef.current = '23'
            typeRef.current = '1'
          }}
        >
          <Tabs defaultActiveKey="23" onChange={onTabChange} items={tabArr} />
          <ExportHistory />
        </Modal>
      )}
    </>
  )
}
