import { intlTrans } from '@/utils/common'
import { useBoolean } from 'ahooks'
import { Button, message } from 'antd'
import { useState } from 'react'
import { DownloadRecordsModal } from '../DownloadRecordsModal'

interface Props {
  downloadRequest: () => Promise<IResponseProps<boolean>>
  childrenType: number
  isDemo?: boolean
}
export const Export = ({ downloadRequest, childrenType, isDemo }: Props) => {
  const [loading, setLoading] = useState(false)
  const [downloadRecordModalOpen, { toggle: downloadRecordModalOpenToggle }] = useBoolean()

  const onDownload = async () => {
    setLoading(true)
    const res = await downloadRequest()
    setLoading(false)
    if (res.code === 200) {
      message.success(intlTrans('common.下载成功请在历史记录里查看'))
    } else {
      message.success(intlTrans('common.OperationFailed'))
    }
  }

  return (
    <>
      <div style={{ display: 'flex' }}>
        <Button type="primary" onClick={onDownload} loading={loading} disabled={isDemo}>
          {intlTrans('common.下载')}
        </Button>
        <div style={{ width: 12 }} />
        <Button onClick={downloadRecordModalOpenToggle} disabled={isDemo}>
          {intlTrans('common.历史记录')}
        </Button>
      </div>
      <DownloadRecordsModal
        open={downloadRecordModalOpen}
        onCancel={downloadRecordModalOpenToggle}
        childrenType={childrenType}
      />
    </>
  )
}
