import { UploadOutlined } from '@ant-design/icons'
import type { RcFile } from 'antd/es/upload/interface'
import { Button, Upload } from 'antd'
import { intlTrans } from '@/utils/common'
import { uploadAchoiceFile } from '@/api/upload'
import { useMemo } from 'react'

interface Props {
  value?: any
  onChange?: (value: any) => void
  accept?: string
  exampleFileUrl?: string
  needUpload?: boolean
}

const FileUploader = ({
  value,
  onChange,
  accept = '.xls, .xlsx, .csv',
  exampleFileUrl,
  needUpload = true
}: Props) => {
  const customRequest = async ({ file, onSuccess }: any) => {
    const formData = new FormData()
    formData.append('file', file as RcFile)
    if (needUpload) {
      const { code, result } = await uploadAchoiceFile(formData)
      if (code === 200) {
        onChange?.(result)
        onSuccess()
      }
    } else {
      onChange?.(file)
      onSuccess()
    }
  }

  const defaultFileList = useMemo(
    () =>
      value
        ? [
            {
              uid: value,
              name: value
            }
          ]
        : [],
    [value]
  )

  const onRemove = () => {
    onChange?.(null)
    return true
  }

  return (
    <Upload
      maxCount={1}
      accept={accept}
      defaultFileList={defaultFileList}
      customRequest={customRequest}
      onRemove={onRemove}
    >
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Button icon={<UploadOutlined />}>{intlTrans('common.点击上传')}</Button>
        &nbsp;&nbsp;
        {exampleFileUrl && (
          <a
            onClick={(event) => {
              event.stopPropagation()
              window.location.href = exampleFileUrl
            }}
          >
            {intlTrans('common.下载模板')}
          </a>
        )}
      </div>
      <p style={{ color: 'gray', fontSize: '13px' }}>
        {`${intlTrans('common.支持扩展名')}: ${accept}`}
      </p>
    </Upload>
  )
}

export default FileUploader
