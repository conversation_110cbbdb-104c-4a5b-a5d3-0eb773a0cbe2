import type { CSSProperties, MouseEvent } from "react";

import styles from "./index.less";

export interface IIconfontProps {
  className?: string;
  icon?: string;
  onClick?: (e: MouseEvent) => void;
  style?: CSSProperties | undefined;
  fontSize?: number;
  color?: string;
}

const Iconfont: React.FC<IIconfontProps> = (props) => {
  const { icon, style = {}, onClick, className, color, fontSize } = props;

  if (color) {
    style.color = color;
  }

  if (fontSize) {
    style.fontSize = fontSize;
  }

  return (
    <svg
      onClick={(e) => onClick?.(e)}
      className={`${styles["svg-icon"]} ${className}`}
      style={style}
      aria-hidden='true'
    >
      <use xlinkHref={`#${icon}`} />
    </svg>
  );
};

export default Iconfont;
