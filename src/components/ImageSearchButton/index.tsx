/*
 * @Author: <PERSON>
 * @Date: 2024-04-28 11:33:36
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-28 15:40:31
 * @Desc 图片搜索按钮
 */
import { uchoiceToken } from '@/utils/auth'
import { getBase64, intlTrans, isImageFile } from '@/utils/common'
import { getUliveAdminBaseUrl } from '@/utils/getBaseUrl'
import { CameraOutlined } from '@ant-design/icons'
import { Button, Image, Upload, message } from 'antd'
import type { RcFile, UploadFile, UploadProps } from 'antd/lib/upload'
import { useCallback, useRef, useState } from 'react'
import './index.less'

const ImageSearchButton = (props: Pick<UploadProps, 'onChange'>) => {
  const { onChange } = props
  const [previewVisible, setPreviewVisible] = useState(false)
  const preview = useRef('')

  const onBeforeUpload = useCallback((file: RcFile) => {
    if (!isImageFile(file.type)) {
      message.error(intlTrans('common.pleaseUploadPic'))
      return Upload.LIST_IGNORE
    }
    const size = file.size / 1024 / 1024
    // 限制上传大小 6MB
    if (size > 6) {
      message.error(intlTrans('common.imageLessThan2MB'))
      return Upload.LIST_IGNORE
    }
  }, [])

  const onPreview = useCallback(async (file: UploadFile) => {
    if (file.preview) {
      preview.current = file.preview
    } else {
      preview.current = file.preview = await getBase64(file.originFileObj as RcFile)
    }
    setPreviewVisible(true)
  }, [])

  return (
    <>
      <Upload
        accept="image/*"
        name="file"
        onChange={onChange}
        onPreview={onPreview}
        beforeUpload={onBeforeUpload}
        action={`${getUliveAdminBaseUrl()}api-base/upload/uploadAchoiceFile`}
        headers={{
          Authorization: `Bearer ${uchoiceToken.get()}`
        }}
        maxCount={1}
      >
        <Button type="primary" icon={<CameraOutlined />} className="img-search-button">
          {intlTrans('product.ImageSearch')}
        </Button>
      </Upload>
      <Image
        style={{ display: 'none' }}
        src={preview.current}
        preview={{
          visible: previewVisible,
          onVisibleChange(value) {
            setPreviewVisible(value)
          }
        }}
      />
    </>
  )
}

export default ImageSearchButton
