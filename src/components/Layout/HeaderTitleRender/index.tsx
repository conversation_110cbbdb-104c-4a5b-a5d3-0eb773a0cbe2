import { useEffect } from 'react'
import * as Sentry from '@sentry/react'
import logo from '@/assets/logo.png'
import styles from './index.less'

let isInit = false

const HeaderTitleRender = () => {
  useEffect(() => {
    if (isInit) return
    isInit = true
    Sentry.init({
      dsn: 'https://<EMAIL>/4508957708386304',
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({
          maskAllText: false,
          blockAllMedia: false
        })
      ],
      environment: process.env.API_ENV,
      // Performance Monitoring
      tracesSampleRate: 1.0, //  Capture 100% of the transactions
      // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
      tracePropagationTargets: [
        'localhost',
        /^https:\/\/h5\.test\.uchoice\.youpik\.dev/,
        /^https:\/\/h5\.prod\.uchoice\.pro/
      ],
      // Session Replay
      replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      replaysOnErrorSampleRate: 1.0 // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
    })
  }, [])

  return (
    <div className={styles.title_render_container}>
      <img src={logo} className={styles.logo_new} />
    </div>
  )
}

export default HeaderTitleRender
