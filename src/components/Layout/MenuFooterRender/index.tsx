import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import { useModel } from 'umi'
import styles from './index.less'

const MenuFooterRender = () => {
  const { initialState, setInitialState } = useModel('@@initialState')

  const toggleCollapse = () => {
    setInitialState({
      settings: {
        ...initialState?.settings,
        collapsed: !initialState?.settings.collapsed
      }
    })
  }

  return (
    <div className={styles.menu_footer_container}>
      {initialState?.settings.collapsed ? (
        <MenuUnfoldOutlined onClick={toggleCollapse} style={{ color: '#009995' }} />
      ) : (
        <MenuFoldOutlined onClick={toggleCollapse} />
      )}
    </div>
  )
}

export default MenuFooterRender
