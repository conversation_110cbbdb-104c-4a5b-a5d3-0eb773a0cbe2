import type { SiderMenuProps } from '@ant-design/pro-layout/lib/components/SiderMenu/SiderMenu'

import styles from './index.less'

export interface IMenuHeaderRenderProps {
  logo?: React.ReactNode
  title?: React.ReactNode
  props?: SiderMenuProps
  className?: string
}

const MenuHeaderRender: React.FC<IMenuHeaderRenderProps> = ({ className, logo, title, props }) => {
  return (
    <div className={`${className} ${styles['logo-container']}`}>
      {logo}
      <span className={styles['logo-right']}>uChoice Pro</span>
      {/* {!props?.collapsed && <img src={consolePng} alt='Console' className={styles["logo-right"]} />} */}

      {/* <img src={logo_new} className={styles.logo_new} /> */}
    </div>
  )
}
//
export default MenuHeaderRender
