import { useState } from 'react'
import type { MenuProps } from 'antd'
import { Menu } from 'antd'
import { DownOutlined } from '@ant-design/icons'

import HeaderDropdown from '@/components/Layout/HeaderDropdown'

import { BASE_URLS } from '@/utils/getBaseUrl'
import { apiEnvStorage } from '@/utils/storage'

import styles from './index.less'
import { isProd } from '@/utils/env'

const getEnvText = (key: TEnvEnmu) => {
  switch (key) {
    case 'DEV':
      return '开发'
    case 'STAGING':
      return '测试'
    case 'PRE':
      return '预发'
    case 'PROD':
      return '正式'
    default:
      return '测试'
  }
}

export interface IEnvProps {
  wrapClassName?: string
}

const Env: React.FC<IEnvProps> = ({ wrapClassName }) => {
  const [env, setEnv] = useState(apiEnvStorage.get() || (process.env.API_ENV as TEnvEnmu))

  const handleClickItem: MenuProps['onClick'] = (it) => {
    const { key } = it
    apiEnvStorage.set(key as TEnvEnmu)
    setEnv(key as TEnvEnmu)
    window.location.reload()
  }

  const menuProps: MenuProps = {
    onClick: handleClickItem,
    selectedKeys: [env],
    items: Object.keys(BASE_URLS).map((it) => ({
      key: it as TEnvEnmu,
      label: getEnvText(it as TEnvEnmu)
    }))
  }

  return isProd() ? null : (
    <HeaderDropdown menu={menuProps} className={wrapClassName}>
      <span className={styles.action}>
        <span className={`${styles.env_desc}`}>{getEnvText(env)}</span>
        <DownOutlined style={{ marginLeft: '5px', fontSize: '12px' }} />
      </span>
    </HeaderDropdown>
  )
}

export default Env
