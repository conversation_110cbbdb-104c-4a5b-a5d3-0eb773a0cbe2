import { useEffect, useRef, useState } from 'react'
import type { MenuProps } from 'antd'
import { Menu, message } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import { getLocale, setLocale, useIntl } from 'umi'
import HeaderDropdown from '@/components/Layout/HeaderDropdown'
import styles from './index.less'
import { isToAuditCountStorage, nationStorage } from '@/utils/storage'
import userApi from '@/api/user'
import { uchoiceToken } from '@/utils/auth'

const getNationText = (key: string, intl: any) => {
  switch (key) {
    case 'TH':
      return <div>🇹🇭 {intl.formatMessage({ id: 'locale.泰国' })}</div>
    case 'VI':
      return <div>🇻🇳 {intl.formatMessage({ id: 'locale.越南' })}</div>
    default:
      return <div>🇹🇭 {intl.formatMessage({ id: 'locale.泰国' })}</div>
  }
}
const lingualArr = ['TH', 'VI']

export interface IEnvProps {
  wrapClassName?: string
  handleSelectedEnation: any
}

const MultiLingual: React.FC<IEnvProps> = ({ wrapClassName, handleSelectedEnation }) => {
  const intl = useIntl()
  const nation = useRef(nationStorage.get() || '')

  const changeLang = (lang: string) => {
    // const lang = getLocale();
    if (lang === 'TH') {
      nation.current = 'TH'
      // setLocale("th-TH", false);
    } else if (lang === 'VI') {
      nation.current = 'VI'
      // setLocale("vi-VN", false);
    }
  }

  useEffect(() => {
    // changeLang();
  }, [])

  const handleClickItem: MenuProps['onClick'] = async (it) => {
    const { key } = it
    if (!uchoiceToken.get()) {
      changeLang(key)
      nationStorage.set(key)
      location.href = `${location.origin}/login?redirect=${encodeURIComponent(location.href)}`
    } else {
      const hide = message.loading(intl.formatMessage({ id: 'locale.正在切换' }), 0, () => {
        isToAuditCountStorage.set('false')
      })
      try {
        const result = await userApi.syncSuppliers(key === 'TH' ? 'TH' : 'VN')
        hide()
        if (result && result.code === 200) {
          handleSelectedEnation(key)
        }
      } catch (error) {
        hide()
      }
    }
  }

  const menuHeaderDropdown = (
    <Menu onClick={handleClickItem} selectedKeys={[nation.current]}>
      {lingualArr.map((it) => (
        <Menu.Item key={it}>{getNationText(it, intl)}</Menu.Item>
      ))}
    </Menu>
  )
  return (
    <HeaderDropdown overlay={menuHeaderDropdown} className={wrapClassName}>
      <span className={styles.action}>
        <span className={`${styles.env_desc} `}>{getNationText(nation.current, intl)}</span>
        <DownOutlined style={{ marginLeft: '5px', fontSize: '12px' }} />
      </span>
    </HeaderDropdown>
  )
}

export default MultiLingual
