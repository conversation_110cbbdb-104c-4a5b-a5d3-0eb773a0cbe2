import { DownOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { Menu } from 'antd'
import { useMemo } from 'react'

import HeaderDropdown from '@/components/Layout/HeaderDropdown'

import styles from './index.less'

import { uchoiceToken } from '@/utils/auth'
import { intlTrans } from '@/utils/common'
import { useHistory } from 'umi'
import { isToAuditCountStorage, userInfoStorage } from '@/utils/storage'

export interface IEnvProps {
  wrapClassName?: string
}

const User: React.FC<IEnvProps> = ({ wrapClassName }) => {
  const history = useHistory()

  const handleClickItem: MenuProps['onClick'] = (it) => {
    const { key } = it
    switch (key) {
      case 'home':
        history.push({
          pathname: '/'
        })
        break
      default:
        uchoiceToken.set('')
        isToAuditCountStorage.set('')
        location.href = `${location.origin}/login?redirect=${encodeURIComponent(location.href)}`
        break
    }
  }

  const menuHeaderDropdown: any = {
    onClick: handleClickItem,
    items: [
      { key: 'home', label: intlTrans('common.HomePage') },
      { key: 'logout', label: intlTrans('login.LogOut') }
    ]
  }

  const user = useMemo(() => {
    const userInfo = JSON.parse(userInfoStorage.get() || '{}')
    return {
      realName: userInfo.name
    }
  }, [])

  return (
    <HeaderDropdown menu={menuHeaderDropdown} className={wrapClassName}>
      <span className={styles.action}>
        <span className={`${styles.env_desc} `}>{user.realName}</span>
        <DownOutlined style={{ marginLeft: '5px', fontSize: '12px' }} />
      </span>
    </HeaderDropdown>
  )
}

export default User
