import { SelectLang } from 'umi'
import { Space } from 'antd'

import Env from './Env'
import User from './User'
import MultiLingual from './MultiLingual'

import { isProduction } from '@/utils/common'
import { nationStorage } from '@/utils/storage'
import { setLanguage } from '@/utils/setNationLang'

export default function RightContentRender() {
  const handleChangEnation = (key: any) => {
    nationStorage.set(key)
    // setLanguage(key);
    window.location.reload()
  }

  return (
    <Space>
      {!isProduction && <Env />}
      <SelectLang />
      <MultiLingual handleSelectedEnation={handleChangEnation} />
      <User />
    </Space>
  )
}
