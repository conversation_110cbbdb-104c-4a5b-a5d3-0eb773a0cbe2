/*
 * @Author: <PERSON>
 * @Date: 2024-04-28 10:35:41
 * @Last Modified by:   <PERSON>
 * @Last Modified time: 2024-04-28 10:35:41
 * @Desc loading button
 */
import { Button } from 'antd'
import type { BaseButtonProps } from 'antd/lib/button/button'
import { memo, useCallback, useState } from 'react'

const LoadingButton = (
  props: Omit<BaseButtonProps, 'loading' | 'onClick'> & { request: () => Promise<any> | any }
) => {
  const [loading, setLoading] = useState(false)
  const { request } = props

  const onRequest = useCallback(async () => {
    setLoading(true)
    try {
      await request()
    } catch (error) {
      console.log(error)
    }
    setLoading(false)
  }, [request])

  return <Button loading={loading} onClick={onRequest} {...props} />
}

export default memo(LoadingButton)
