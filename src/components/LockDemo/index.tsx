import { intlTrans } from '@/utils/common'
import { Button } from 'antd'
import type { BaseButtonProps } from 'antd/lib/button/button'
import { memo, useCallback, useState } from 'react'
import styles from './index.less'
import { CheckCircleTwoTone, SafetyCertificateFilled } from '@ant-design/icons'
import { history } from 'umi'
import shopApi from '@/api/shop'
interface prop {
  onShowDemo: () => void
  type: number
}
const LockDemo = ({ onShowDemo, type }: prop) => {
  const arr = [
    intlTrans('analytics.解锁视频投流码不用苦苦向达人索取'),
    intlTrans('analytics.解锁ROI分析投入产出一目了然'),
    intlTrans('analytics.支持本地跨境店按站点统计无需数据再整合')
  ]
  const [authDisabled, setAuthDisabled] = useState(false)

  const handleToTiktokShop = async () => {
    try {
      setAuthDisabled(true)
      const url = type == 1 ? '/commodity/manage' : '/dataAnalytics/videoPerformance'
      const result = await shopApi.getTiktokAuthUrl(url)
      setAuthDisabled(false)
      if (result.code == 200 && result.result) {
        window.open(result.result, '_blank')
      }
    } catch (error) {
      setAuthDisabled(false)
    }
  }
  return (
    <div className={styles.box}>
      <SafetyCertificateFilled className={styles.icon} />
      <div className={styles.content}>
        <div className={styles.title}>{intlTrans('analytics.授权TikTok店铺立即解锁')}</div>
        <div className={styles.tip}>
          {arr.map((item) => {
            return (
              <div key={item} className={styles.tip_item}>
                <CheckCircleTwoTone twoToneColor="#52c41a" />
                &nbsp;&nbsp;
                <span>{item}</span>
              </div>
            )
          })}
        </div>
        <div className={styles.btns}>
          <Button
            className={styles.btn}
            onClick={() => {
              onShowDemo()
            }}
          >
            {intlTrans('analytics.查看Demo')}
          </Button>
          <Button
            type="primary"
            className={styles.authorize_btn}
            onClick={handleToTiktokShop}
            disabled={authDisabled}
          >
            {intlTrans('analytics.立即授权')}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default memo(LockDemo)
