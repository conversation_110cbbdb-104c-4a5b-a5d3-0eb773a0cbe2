import { intlTrans } from '@/utils/common'
import { Button, type ModalProps, Modal as AntdModal } from 'antd'
import { useMemo } from 'react'

type MethodModalProps = ModalProps & { othersButton?: React.ReactNode }
type InfoModalProps = Omit<MethodModalProps, 'onOk' | 'okText' | 'okButtonProps' | 'okType'>

const Modal = (props: ModalProps) => {
  const {
    mask = true,
    destroyOnClose = true,
    footer = undefined,
    width = 500,
    maskClosable = false,
    centered = true,
    ...resetProps
  } = props
  return (
    <AntdModal
      {...resetProps}
      mask={mask}
      destroyOnClose={destroyOnClose}
      width={width}
      footer={footer}
      centered={centered}
      maskClosable={maskClosable}
    >
      {props.children}
    </AntdModal>
  )
}

/**
 * 确认弹窗
 * @param props
 * @returns
 */
const Confirm = (props: MethodModalProps) => {
  const { okText, onOk, cancelText, onCancel, okButtonProps, cancelButtonProps, othersButton } =
    props
  const Footer = useMemo(() => {
    return (
      <>
        {othersButton}
        <Button type="primary" {...okButtonProps} onClick={onOk}>
          {okText ? okText : intlTrans('common.Sure')}
        </Button>
        <Button onClick={onCancel} {...cancelButtonProps}>
          {cancelText ? cancelText : intlTrans('common.Cancel')}
        </Button>
      </>
    )
  }, [othersButton, okButtonProps, onOk, okText, onCancel, cancelButtonProps, cancelText])

  return <Modal {...props} footer={Footer} />
}

/**
 * 消息弹窗
 * @param props
 * @returns
 */
const Info = (props: InfoModalProps) => {
  const { cancelText, onCancel, cancelButtonProps, othersButton } = props

  const Footer = useMemo(() => {
    return (
      <>
        {othersButton}
        <Button onClick={onCancel} {...cancelButtonProps}>
          {cancelText ? cancelText : intlTrans('common.Close')}
        </Button>
      </>
    )
  }, [othersButton, onCancel, cancelButtonProps, cancelText])

  return <Modal {...props} footer={Footer} />
}

Modal.Confirm = Confirm

Modal.Info = Info

export default Modal
