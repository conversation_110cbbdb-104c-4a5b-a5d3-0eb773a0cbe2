import type { IDomEditor, IEditorConfig } from '@wangeditor/editor'
import { Editor, Toolbar } from '@wangeditor/editor-for-react'
import '@wangeditor/editor/dist/css/style.css' // 引入 cssß
import { useEffect, useState } from 'react'

import { uploadImage } from '@/api/upload'
import { intlTrans } from '@/utils/common'
import type { SlateElement } from '@wangeditor/editor'
import { i18nAddResources, i18nChangeLanguage } from '@wangeditor/editor'
import { getLocale } from 'umi'
import THTranslate from './th-TH_translate'

export type ImageElement = SlateElement & {
  src: string
  alt: string
  url: string
  href: string
}
export type InsertFnType = (url: string, alt: string, href: string) => void

i18nAddResources('th-TH', THTranslate)

function MyEditor(props: any) {
  // editor 实例
  const [editor, setEditor] = useState<IDomEditor | any>(null)

  // 编辑器内容
  // const [html, setHtml] = useState('')

  // 工具栏配置
  // const toolbarConfig: Partial<IToolbarConfig> = {
  //   //去除的选项
  //   excludeKeys: [
  //     'headerSelect', //正文，标题
  //     'blockquote', //引用
  //     // "|",//隔符
  //     'bold', //加粗
  //     'underline', //下划线
  //     'italic', //斜体
  //     'group-more-style', //样式
  //     'color', //颜色
  //     'bgColor', //背景色
  //     'fontSize', //字体大小
  //     'fontFamily', //字体
  //     // "lineHeight",行高
  //     'bulletedList', //无序列表
  //     'numberedList', //有序列表
  //     'todo', //待办
  //     'group-justify', //对齐
  //     'group-indent', //缩进
  //     // "emotion",//表情
  //     'insertLink', //链接
  //     // "group-image", //图片
  //     'group-video', //视频
  //     'insertTable', //插表格
  //     'codeBlock', //代码块
  //     'divider' //分割线
  //     // "undo",//撤销
  //     // "redo",//重做
  //     // "fullScreen"//全屏
  //   ]
  // }
  // 编辑器配置
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: intlTrans('common.PleaseEnter'),
    autoFocus: false,
    //插图
    MENU_CONF: {
      uploadImage: {
        // 单个文件的最大体积限制，默认为 2M
        maxFileSize: 6 * 1024 * 1024, // 1M

        // 最多可上传几个文件，默认为 100
        maxNumberOfFiles: 10,

        // 超时时间，默认为 10 秒
        timeout: 5 * 1000, // 5 秒

        // 用户自定义上传图片
        // 用户自定义上传图片
        async customUpload(file: any, insertFn: any) {
          try {
            const data = new FormData()
            // file 即选中的文件 主要就是这个传的参数---重点
            data.append('file', file)
            const { result } = await uploadImage(data)
            insertFn(result)
          } catch (error) {
            // ignore error
          }
        }
      }
    }
  }

  // useEffect(() => {
  //   setHtml(props.htmlContent) //设置编辑器内容
  //   // console.log('123123', editor?.isFocused())
  //   // if (editor?.isFocused()) {

  //   // }
  // }, [props.htmlContent])

  // 及时销毁 editor ，重要！
  useEffect(() => {
    // 必须获取到焦点才能插入
    editor?.focus()
    editor?.dangerouslyInsertHtml(props.htmlContent)
    // editor?.setHtml(props.htmlContent)

    // editor?.blur()
    // const toolbar = DomEditor.getToolbar(editor); //查看配置
    // console.log("qz-toolbar", toolbar);
    i18nChangeLanguage(getLocale())
    return () => {
      if (editor === null) {
        return
      }
      editor.destroy()
      setEditor(null)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editor])

  return (
    <>
      <div style={{ border: '1px solid #ccc', zIndex: 100 }}>
        <Toolbar
          editor={editor}
          // defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc' }}
        />
        <Editor
          defaultConfig={editorConfig}
          // value={html}
          onCreated={setEditor}
          onChange={(editor) => {
            // setHtml(editor.getHtml())
            props.onChange(editor.getHtml())
          }}
          mode="default"
          style={{ height: '500px', overflowY: 'hidden' }}
        />
      </div>
      {/* <div style={{ marginTop: "15px" }}>{html}</div> */}
    </>
  )
}

export default MyEditor
