const THTranslate = {
  header: {
    title: 'หัวกระดาษ',
    text: 'ข้อความ'
  },
  blockQuote: {
    title: 'บล็อกอ้างถึง'
  },
  textStyle: {
    bold: 'ตัวหนา',
    underline: 'ขีดเส้นใต้',
    italic: 'ตัวเอียง',
    through: 'ผ่าน',
    code: 'รหัสอินไลน์',
    sup: 'ว่า ไงอ่ะ',
    sub: 'ย่อย'
  },
  color: {
    color: 'สี',
    default: 'สีปริยาย',
    clear: 'ล้างรูปแบบ',
    bgColor: 'สีหลัง'
  },
  fontSize: {
    title: 'ขนาดตัวอักษร',
    default: 'ค่าปริยาย'
  },
  fontFamily: {
    default: 'ค่าปริยาย',
    title: 'ตระกูลแบบอักษร'
  },
  lineHeight: {
    default: 'ค่าปริยาย',
    title: 'ความสูงของเส้น'
  },
  listModule: {
    unOrderedList: 'ไม่มีการระบุชื่อ',
    orderedList: 'รายการ ที่มีการสั่ง'
  },
  todo: {
    todo: 'สิ่ง ที่จะทำ'
  },
  justify: {
    left: 'ด้านซ้าย',
    right: 'ถูกต้อง',
    center: 'จัดกึ่งกลาง',
    justify: 'ปรับ แต่ง'
  },
  indent: {
    increase: 'เพิ่ม',
    decrease: 'ลดลง'
  },
  emotion: {
    title: 'อารมณ์'
  },
  link: {
    insert: 'แทรก'
  },
  image: {
    netImage: 'ภาพลัพธ์',
    src: 'ภาพ src',
    desc: 'คำอธิบาย',
    link: 'ตำแหน่งเชื่อมโยงภาพ'
  },
  uploadImgModule: {
    uploadImage: 'อัปโหลดรูปภาพ'
  },
  videoModule: {
    insertVideo: 'แทรกวิดีโอ',
    uploadVideo: 'อัปโหลดวิดีโอ',
    videoSrc: 'แหล่งวิดีโอ',
    videoPoster: 'โปสเตอร์วิดีโอ',
    videoSrcPlaceHolder: 'ที่อยู่ url ของแฟ้มวิดีโอ หรือบุคคล ที่สาม < iframe >',
    videoPosterPlaceHolder: 'ที่อยู่ url ของภาพแบบโปสเตอร์',
    ok: 'โอเคค่ะ'
  },
  tableModule: {
    insertTable: 'แทรกตาราง'
  },
  codeBlock: {
    title: 'บล็อครหัส'
  },
  divider: {
    title: 'ตัวแบ่ง'
  },
  undo: {
    undo: 'เลิกทำ',
    redo: 'ทำซ้ำ'
  },
  fullScreen: {
    title: 'ความสงสัยเต็มรูปแบบ'
  },
  common: {
    ok: 'โอเคค่ะ'
  }
}

export default THTranslate
