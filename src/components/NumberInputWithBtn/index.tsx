import React from 'react'
import { InputNumber } from 'antd'

interface NumberInputWithBtnProps {
  value?: string | number
  onChange: (value: string) => void
  maxLength?: number
  showCount?: boolean
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void
  widthStyle?: React.CSSProperties
  isWithPoint?: boolean
  addonBefore?: React.ReactNode
  replenishPoint?: boolean
  [key: string]: any
}

const NumberInputWithBtn: React.FC<NumberInputWithBtnProps> = ({
  value,
  onChange,
  maxLength,
  showCount,
  onBlur,
  widthStyle,
  isWithPoint = false,
  addonBefore,
  replenishPoint,
  ...others
}) => {
  const handleInputOnChange = (e: any) => {
    const changedValue = e?.toString() || ''
    if (isWithPoint) {
      const formattedValue = changedValue.replace(/^\D*(\d{1,8})(\.\d{0,2})?.*$/, '$1$2')
      onChange(formattedValue)
    } else if (replenishPoint) {
      const formattedValue = changedValue.replace(/^(\d+)(\.\d{0,2})?.*$/, '$1$2')
      onChange(formattedValue)
    } else {
      const formattedValue = changedValue.replace(/\D/g, '')
      onChange(formattedValue)
    }
  }

  const defaultWidthStyle = { width: '100%' }
  const mergedStyle = { ...defaultWidthStyle, ...widthStyle }

  return (
    <InputNumber
      value={value}
      autoComplete="off"
      onBlur={onBlur}
      addonBefore={addonBefore}
      style={mergedStyle}
      {...others}
      min={1}
      precision={isWithPoint ? 2 : 0}
      onChange={handleInputOnChange}
    />
  )
}

export default NumberInputWithBtn
