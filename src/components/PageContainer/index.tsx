import type { PageContainerProps } from "@ant-design/pro-components";
import { PageContainer } from "@ant-design/pro-components";

import styles from "./index.less";

const MPageContainer: React.FC<PageContainerProps> = (props) => {
  const { className, ...resetProps } = props;
  return (
    <PageContainer
      breadcrumbRender={false}
      pageHeaderRender={false}
      title={false}
      className={`${styles["page-container"]} ${className}`}
      {...resetProps}
    />
  );
};

export default MPageContainer;
