/*
 * @Author: <PERSON>
 * @Date: 2024-05-11 10:36:12
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-27 11:57:55
 * @Desc 列表中的商品图片
 */
import { useBoolean } from 'ahooks'
import { Image, Spin } from 'antd'
import { useEffect, useMemo, useState, type CSSProperties } from 'react'
import { defaultImage } from '../../assets/default_avatar.png'
interface Props {
  src?: string
  width?: string | number
  height?: string | number
  isNoPreview?: boolean
}

const ProductImageStyle: CSSProperties = {
  objectFit: 'contain',
  backgroundColor: 'transparent'
}

const SpinStyle: CSSProperties = {
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}

const ProductImage = (props: Props) => {
  const { width = 60, height = 80 } = props
  const [srcArr, setSrcArr] = useState<string[]>([])
  const [previewVisible, { toggle: previewVisibleToggle }] = useBoolean()

  useEffect(() => {
    const src: any = props.src || ''
    if (src) {
      const srcArr = src.split(',')
      setSrcArr(srcArr)
    }
  }, [props.src])

  const srcMap = useMemo(() => {
    return srcArr.slice(1)
  }, [srcArr])
  const handleImageError = (e) => {
    // 阻止默认的错误处理行为
    e.preventDefault()
    // 将图片的src属性设置为默认头像的地址
    e.target.src = defaultImage
  }
  return (
    <>
      {props.isNoPreview ? (
        <Image
          src={srcArr[0]}
          preview={false}
          width={width}
          height={height}
          placeholder={<Spin spinning style={SpinStyle} />}
          style={{ ...ProductImageStyle, minWidth: width }}
          loading="lazy"
          onError={handleImageError}
        />
      ) : (
        <Image.PreviewGroup
          preview={{
            visible: previewVisible,
            onVisibleChange: previewVisibleToggle
          }}
        >
          <Image
            src={srcArr[0]}
            width={width}
            height={height}
            onError={handleImageError}
            placeholder={<Spin spinning style={SpinStyle} />}
            style={{ ...ProductImageStyle, minWidth: width }}
            loading="lazy"
          />
          {previewVisible && srcMap.map((src) => <Image key={src} src={src} hidden />)}
        </Image.PreviewGroup>
      )}
    </>
  )
}

export default ProductImage
