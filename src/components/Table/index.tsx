/*
 * @Author: <PERSON>
 * @Date: 2024-04-17 14:59:18
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-23 18:07:04
 * @Desc 表格组件
 */
import { intlTrans } from '@/utils/common'
import type { ActionType, BaseQueryFilterProps, ProTableProps } from '@ant-design/pro-components'
import { ProTable } from '@ant-design/pro-components'
import type { FormInstance, TableProps } from 'antd'
import { Space } from 'antd'
import { Button } from 'antd'
import classNames from 'classnames'
import { useMemo, useRef, useState } from 'react'
import './index.less'

/**
 * U-请求参数类型
 * T-列表数据类型
 */
type Props = ProTableProps<any, any> & {
  searchSpan?: BaseQueryFilterProps['span']
  searchExtra?: React.ReactNode[]
}

const Table = (props: Props) => {
  const { searchSpan = 4, className, searchExtra, ...otherProps } = props
  const actionRef = useRef<ActionType | undefined>(undefined)
  const [isEmpty, setIsEmpty] = useState(true)

  const scroll = useMemo(() => {
    const res: TableProps<any>['scroll'] = { x: 'max-content', ...otherProps.scroll }
    return res
  }, [otherProps.scroll])

  return (
    <ProTable
      className={classNames('custom-pro-table', className, {
        'is-empty': isEmpty
      })}
      actionRef={actionRef}
      options={{
        reload: true,
        setting: false,
        density: false
      }}
      search={{
        span: searchSpan,
        labelWidth: 'auto',
        searchText: intlTrans('common.Search'),
        resetText: intlTrans('common.Reset'),
        collapseRender: () => null,
        collapsed: false,
        optionRender: (searchConfig, formProps, dom) => [
          // ...dom,
          // searchExtra ? <div style={{ width: 20 }} /> : null,
          ...(searchExtra || [])
        ]
      }}
      pagination={{
        showQuickJumper: true,
        defaultPageSize: 10,
        showSizeChanger: true,
        position: ['bottomRight'],
        showTotal(total) {
          return `${intlTrans('common.Total')} ${total}`
        }
      }}
      onLoad={(dataSource) => {
        setIsEmpty(dataSource.length === 0)
      }}
      // 可覆盖以上属性
      {...otherProps}
      scroll={scroll}
    />
  )
}

export default Table
