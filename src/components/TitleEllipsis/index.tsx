import React from 'react'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import styles from './index.less'
import { Tooltip } from 'antd'

interface ITipProps {
  content: string
  line?: number
}

const TitleEllipsis: React.FC<ITipProps> = ({ content, line }) => {
  return (
    <Tooltip title={content}>
      <div className={styles.title} style={{ WebkitLineClamp: line || 1 }}>
        {content}
      </div>
    </Tooltip>
  )
}

export default TitleEllipsis
