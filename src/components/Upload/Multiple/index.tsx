import { Modal, Upload } from 'antd'
import { useState } from 'react'

import Iconfont from '@/components/Iconfont'

import config from '../config'

import type { RcFile, UploadFile, UploadProps } from 'antd/lib/upload'
import styles from './index.less'

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })

export interface IMutipleUploadProps extends Omit<UploadProps, 'onChange'> {
  onChange: (fileList: UploadFile[]) => void
}

const MultipleUpload: React.FC<IMutipleUploadProps> = (props) => {
  const [previewVisible, setPreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [previewTitle, setPreviewTitle] = useState('')

  const { onChange, fileList = [], maxCount = 9, ...resetProps } = props

  const handleCancel = () => setPreviewVisible(false)

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile)
    }

    setPreviewImage(file.url || (file.preview as string))
    setPreviewVisible(true)
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1))
  }

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    onChange?.(newFileList)
  }

  const uploadButton = (
    <div>
      <Iconfont icon="dragger-upload-icon" fontSize={72} />
    </div>
  )

  return (
    <>
      <Upload
        {...config}
        listType="picture-card"
        multiple
        className={`${styles['upload-multiple']}`}
        fileList={fileList}
        maxCount={maxCount}
        onPreview={handlePreview}
        onChange={handleChange}
        {...resetProps}
      >
        {fileList.length >= maxCount ? null : uploadButton}
      </Upload>
      <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={handleCancel}>
        <img
          alt="example"
          style={{
            width: '100%'
          }}
          src={previewImage}
        />
      </Modal>
    </>
  )
}

export default MultipleUpload
