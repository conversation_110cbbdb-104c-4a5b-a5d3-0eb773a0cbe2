import type { UploadProps } from "antd";

import { uchoiceToken } from "@/utils/auth";
import { getUliveAdminBaseUrl } from "@/utils/getBaseUrl";

const config: UploadProps = {
  name: "file",
  multiple: true,
  action: getUliveAdminBaseUrl() + "api-base/upload/uploadFile",
  headers: {
    Authorization: "Bearer " + uchoiceToken.get()
  },
  data: {
    isPreview: false,
    businessType: "live"
  }
};

export default config;
