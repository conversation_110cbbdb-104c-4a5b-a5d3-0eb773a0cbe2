import React from "react";
import { useIntl } from "umi";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import styles from "./index.less";

interface ICollapsedProps {
  collapsed: boolean;
}

const Collapsed: React.FC<ICollapsedProps> = ({ collapsed = true }) => {
  const intl = useIntl();

  return collapsed ? <div className={styles.collapsed_container}>
    {/* <div className={styles.label}>{intl.formatMessage({ id: "common.expand" })}</div> */}
    <div className={styles.label}>更多筛选</div>
    <DownOutlined className={styles.arrow} />
  </div> : <div className={styles.collapsed_container}>
    {/* <div className={styles.label}>{intl.formatMessage({ id: "common.collapse" })}</div> */}
    <div className={styles.label}>收起筛选</div>
    <UpOutlined className={styles.arrow} />
  </div>;
};

export default Collapsed;
