import React from 'react'
import { Input } from 'antd'
import { useIntl } from 'umi'

const NumberInput = ({ value, onChange, ...otherProps }: any) => {
  const intl = useIntl()

  const handleInputOnChange = (e: any) => {
    const changedValue = e.target.value
    onChange(changedValue.replace(/\D/g, ''))
  }

  return (
    <Input
      value={value}
      onChange={handleInputOnChange}
      placeholder={intl.formatMessage({ id: 'common.PleaseEnter' })}
      {...otherProps}
    />
  )
}

export default NumberInput
