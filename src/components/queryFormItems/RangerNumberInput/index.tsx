import { InputNumber } from 'antd'
import NumberInput from '../NumberInput'

const RangeNumberInput = ({ value = [null, null], onChange }: { value: any; onChange: any }) => {
  const handleNum1Change = (num: string) => {
    const tempValue = JSON.parse(JSON.stringify(value))
    tempValue[0] = num ? parseInt(num) : null
    onChange(tempValue)
  }

  const handleNum2Change = (num: string) => {
    const tempValue = JSON.parse(JSON.stringify(value))
    tempValue[1] = num ? parseInt(num) : null
    onChange(tempValue)
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <NumberInput min={0} value={value[0]} onChange={handleNum1Change} />-
      <NumberInput min={0} value={value[1]} onChange={handleNum2Change} />
    </div>
  )
}

export default RangeNumberInput
