export const ULIVE_ADMIN_ENV_LOCALE_KEY = 'api_base_url_env'

export const GROW_TOKEN_COOKIE_KEY = 'Youpik-Grow-Token'

/**
 * 商品类型
 */
export const enum ItemType {
  Choice = 1,
  Tiktok = 2
}

/**
 * 申请库存操作类型
 */
export const enum ApplyStockType {
  Add = 1,
  Reduce = 2
}

/**
 * 商品上下架状态
 */
export const enum ProductStatus {
  Delist,
  Putaway
}

/**
 * 订单类型
 */
export const enum OrderType {
  /**
   * 未下单
   */
  DoNotOrder,
  /**
   * 征程订单
   */
  NormalOrder,
  /**
   * 异常订单
   */
  AbnormalOrder
}

/**
 * 订单状态
 */
export const enum OrderStatus {
  TOTAL = 'TOTAL',
  /**
   * 未支付
   */
  UNPAID = 'UNPAID',
  /**
   * 等待发货
   */
  AWAITING_SHIPMENT = 'AWAITING_SHIPMENT',
  /**
   * 等待揽收
   */
  AWAITING_COLLECTION = 'AWAITING_COLLECTION',
  /**
   * 运输中
   */
  IN_TRANSIT = 'IN_TRANSIT',
  /**
   * 已交付
   */
  DELIVERED = 'DELIVERED',
  /**
   * 已完成
   */
  COMPLETED = 'COMPLETED',
  /**
   * 已取消
   */
  CANCELLED = 'CANCELLED',
  /**
   * 异常订单
   */
  ABNORMAL_ORDERS = 'ABNORMAL_ORDERS'
}

/**
 * Lazada订单状态
 */
export const enum LazadaOrderStatus {
  /**
   * 全部
   */
  TOTAL = '1000',
  /**
   * 已下单
   */
  ORDERED = '1001',
  /**
   * 已出库
   */
  OUTBOUND = '1002',
  /**
   * 已揽收
   */
  COLLECTED = '1003',
  /**
   * 已关闭
   */
  CLOSED = '1004',
  /**
   * 下单失败
   */
  ORDER_PLACEMENT_FAILED = '1005',
  /**
   * 取消中
   */
  CANCELING = '1006',
  /**
   * 取消失败
   */
  CANCEL_FAILED = '1007'
}

/**
 * Lazada订单状态字符串
 */
export const getLazadaOrderStatusStr = (value: number, exchanging: boolean, intlTrans: any) => {
  if (value === 1001) return intlTrans('order.Lazada_已下单')
  if (value === 1002) return intlTrans('order.Lazada_已出库')
  if (value === 1003) return intlTrans('order.Lazada_已揽收')
  if (value === 1004) return intlTrans('order.Lazada_已关闭')
  if (value === 1005 && !exchanging) return intlTrans('order.Lazada_下单失败')
  if (value === 1005 && exchanging) return intlTrans('order.Lazada_已下单换货下单失败')
  if (value === 1006 && !exchanging) return intlTrans('order.Lazada_已下单取消中')
  if (value === 1006 && exchanging) return intlTrans('order.Lazada_已下单换货取消中')
  if (value === 1007 && !exchanging) return intlTrans('order.Lazada_已下单取消失败')
  if (value === 1007 && exchanging) return intlTrans('order.Lazada_已下单换货取消失败')
  return intlTrans('order.Lazada_已下单')
}

/**
 * 订单取消状态
 */
export const enum OrderCancelStatus {
  NOT_YET,
  CANCELLED,
  CANCEL_FAILED,
  CANCELING
}

export const enum AddProductFailedCode {
  /**
   * 需要上传尺寸图
   */
  UPLOAD_SIZE = 12052673,

  /**
   * 需要详情描述
   */
  UPLOAD_DETAIL = 12052013,

  /**
   * 需要详情描述
   */
  UPLOAD_DETAIL2 = 12052015,

  /**
   * 修改商品名称
   */
  UPDATE_PRODUCT_NAME = 12052051
}
