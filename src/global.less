@import './assets/styles/header.less';
@import './assets//styles//reset.less';

// @font-face
// {
// 	font-family: Prompt_uChoice;
// 	src: url('./assets//fonts/Prompt-Regular.ttf')
// 		,url('./assets/fonts/Prompt-SemiBold.ttf');
// }

// body {
//   font-family: 'Prompt_uChoice', sans-serif !important;
// }

// .ant-typography {
//   font-family: 'Prompt_uChoice', sans-serif !important;
// }

#root {
  height: 100%;
}

#root .ant-pro-global-header-layout-mix {
  background-color: #032023;
}

#root .ant-table-thead > tr > th {
  padding: 11px 16px;
  color: #474e66;
  font-size: 14px;
  line-height: 20px;
  // 表格header背景颜色
  background: #f5f5ff;
}

#root .ant-layout-content {
  padding: 16px;
  background: #f1f1f3;
}

.ant-modal-body .ant-table-thead > tr > th {
  color: #474e66;
  // 表格header背景颜色
  background: #f5f5ff;
}

#root .ant-pro-table .ant-pro-table-search {
  margin-bottom: 0;
}

.ant-menu-sub .ant-pro-menu-item .ant-pro-menu-item-title {
  font-size: 12px;
}

.ant-table-tbody .ant-table-cell {
  white-space: pre-wrap;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #ebf7f7 !important;
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  // 表格选中后背景颜色
  background-color: #ebf7f7;
}

.ant-menu-sub .ant-menu-item {
  padding-left: 42px !important;
}

.spin-loading {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 200;
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.5);

  .ant-spin-dot {
    margin-left: 20%;
  }
}
