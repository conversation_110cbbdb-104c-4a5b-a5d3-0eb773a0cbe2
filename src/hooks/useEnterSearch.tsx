import { useEffect } from "react";
import { useIntl } from "umi";

export const useEnterSearch = () => {
  const intl = useIntl();

  useEffect(() => {
    const listener = ({ key }: { key: string }) => {
      if (key === "Enter") {
        for (const button of document.getElementsByTagName("button")) {
          if (button.innerText?.replace(" ", "") === intl.formatMessage({ id: "button.Search" })) {
            button.click();
          }
        }
      }
    };

    document.addEventListener("keypress", listener);

    return () => document.removeEventListener("keypress", listener);
  }, []);
};
