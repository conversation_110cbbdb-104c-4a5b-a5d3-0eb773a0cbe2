import { getOrderList, type GetOrderListParams } from '@/api/order'

const useGetOrderList = (orderType?: number) => {
  const onGetOrderList = async (params: GetOrderListParams) => {
    try {
      const { result } = await getOrderList({ ...params, orderType })
      return Promise.resolve(result)
    } catch (error) {
      return Promise.resolve({ list: [], total: 0 })
    }
  }

  return onGetOrderList
}

export default useGetOrderList
