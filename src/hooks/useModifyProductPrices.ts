import type { ModifyProductPricesParams } from '@/api/product'
import { modifyProductPrices } from '@/api/product'
import { intlTrans } from '@/utils/common'
import { message } from 'antd'
import { useCallback } from 'react'

const useModifyProductPrices = () => {
  const onModifyProductPrices = useCallback(async (params: ModifyProductPricesParams) => {
    try {
      const { message: msg } = await modifyProductPrices(params)
      message.success(msg)
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }, [])

  return onModifyProductPrices
}

export default useModifyProductPrices
