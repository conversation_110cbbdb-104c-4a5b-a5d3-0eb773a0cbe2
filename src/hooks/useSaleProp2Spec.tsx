import { useMemo } from 'react'

const useSaleProp2Spec = (saleProperties: API.ChoiceSku['saleProperties'] = []) => {
  const specs = useMemo(() => {
    if (saleProperties.length === 0) {
      return null
    }
    console.log(saleProperties)
    return saleProperties.map((item, index) => {
      // eslint-disable-next-line react/no-array-index-key
      return <p key={`spec-${index}`}>{item}</p>
    })
  }, [saleProperties])

  return specs
}

export default useSaleProp2Spec
