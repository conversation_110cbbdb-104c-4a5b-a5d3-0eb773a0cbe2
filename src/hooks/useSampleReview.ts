import React, { useEffect } from 'react'
import { ordersStats } from '@/api/sample'
import { isToAuditCountStorage } from '@/utils/storage'

export default function useSampleReview() {
  const onOrderStatus = async () => {
    const params: any = {
      status: 0,
      noShowError: true
    }
    const res = await ordersStats(params)
    if (res.code === 200) {
      const { toBeAuditCount } = res.result
      if (toBeAuditCount) {
        isToAuditCountStorage.set('true')
      } else {
        isToAuditCountStorage.set('false')
      }
    }
  }

  useEffect(() => {
    onOrderStatus()
  }, [])
}
