/*
 * @Author: <PERSON>
 * @Date: 2024-04-25 11:06:52
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-25 11:20:14
 * @Desc 设置备注hook
 */
import { setRemark } from '@/api/product'
import type { ItemType } from '@/constants'

const uesSetRemarkRequest = (itemType: ItemType) => {
  const onSetRemark = async (data: API.RemarkData) => {
    try {
      const result = await setRemark({ itemType, ...data })
      return Promise.resolve(result)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  return onSetRemark
}

export default uesSetRemarkRequest
