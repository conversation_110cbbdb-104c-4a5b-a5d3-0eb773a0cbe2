import React, { useEffect } from 'react'
import { suppliersInfo } from '@/api/analytics'
import { isAuthShopStorage } from '@/utils/storage'

export default function useShopAuthLock() {
  const isHaveAuthShop = () => {
    suppliersInfo().then((res) => {
      if (res.code == 200 && res.result) {
        const { isAuthShop } = res.result
        if (isAuthShop) {
          isAuthShopStorage.set('true')
        } else {
          isAuthShopStorage.set('false')
        }
      }
    })
  }

  useEffect(() => {
    isHaveAuthShop()
  }, [])
}
