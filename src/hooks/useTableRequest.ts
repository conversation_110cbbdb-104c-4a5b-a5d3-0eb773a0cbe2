/*
 * @Author: <PERSON>
 * @Date: 2024-04-25 18:29:42
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-05-27 11:10:29
 * @Desc 表格request hook
 */
import { cancelRequest } from '@/services/http'
import type { ProTableProps } from '@ant-design/pro-components'

type TableRequest<T, U> = NonNullable<ProTableProps<T, U>['request']>

const useTableRequest = <T = any, U = any>(
  request: (params: U) => Promise<IResponseListProps<T>>
) => {
  const onRequest: TableRequest<T, U> = async (params, sorter) => {
    try {
      const { current: pageNo = 1, ...otherParams } = params
      const sortParams: any = {}
      if (Object.keys(sorter).length !== 0) {
        for (const [key, value] of Object.entries(sorter)) {
          if (key && key != 'undefined') {
            sortParams.sortBy = key
            sortParams.sortDirection = value === 'ascend' ? 'asc' : 'desc'
          }
        }
      }
      const {
        result: { list = [], total = 0 }
      } = await request({ ...otherParams, pageNo, ...sortParams } as U)
      return Promise.resolve({
        data: list,
        success: true,
        total
      })
    } catch (error) {
      return Promise.resolve({
        success: false,
        data: [],
        total: 0
      })
    }
  }

  return onRequest
}

export default useTableRequest
