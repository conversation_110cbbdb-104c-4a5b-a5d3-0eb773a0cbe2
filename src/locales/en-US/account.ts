export default {
  机审配置: 'Automated Review Configuration',
  样品申请机审配置: 'Sample Application Automated Review Configuration',
  TikTok店铺: 'TikTok Shop',
  店铺类型: 'Shop Type',
  授权状态: 'Authorization Status',
  操作: 'Action',
  店铺代码: 'Shop Code',
  '跨境店铺(泰国越南)': 'Cross-border Shop',
  '本地店铺(泰国)': 'Local Shop',
  已设置: 'Set',
  未设置: 'Not Set',
  销量: 'Sales Volume',
  粉丝: 'Followers',
  编辑机审条件: 'Edit Automated Review Conditions',
  开通机审: 'Enable Automated Review',
  设置样品机审通过条件: 'Set {{word}} Sample Automated Review Approval Conditions',
  机审开通状态: 'Automated Review Activation Status',
  取消: 'Cancel',
  确定: 'Confirm',
  请输入必填项: 'Please enter required fields!',
  销量条件: 'Sales Volume Condition',
  件: 'Pieces',
  粉丝数条件: 'Followers Condition',
  千: 'Thousands',
  确认样品机审通过条件: 'Confirm {{word}} Sample Automated Review Approval Conditions',
  生效后符合通过条件的达人样品申请将自动通过商家审核你还要继续吗: `<span style="color: red;">Once effective, creator sample request meeting the approval conditions will be automatically approved by the seller. </span> Do you want to continue?`,
  确认关闭样品机审: 'Confirm to Close {{word}} Sample Automated Review',
  关闭后所有样品将由商家手动审核你还要继续吗: `<span style="color: red;">After closing, all samples will be manually reviewed by the seller. </span> Do you want to continue?`,
  账号管理: 'Account Management',
  TikTok店铺授权: 'TikTok Shop Authorization',
  TikTok店铺授权账号: 'TikTok Shop Authorized Account',
  授权店铺: '+Authorize Shop',
  已绑定: 'Authorized',
  未绑定: 'Not Authorized',
  解绑: 'De-authorized',
  立即绑定: 'Authorize Now',
  店铺授权须知: 'Shop Authorization Notes',
  店铺授权后可查看联盟带货达人的数据效果获取素材adscode:
    'After shop authorization, you can view the performance data of affiliated creators and obtain the video ads code',
  Youpik承诺保护商家的店铺数据安全所有店铺仅用于帮助商家数据分析使用:
    `Youpik commits to protecting sellers' shop data security, all shops are only used for helping seller with data analysis`,
  机审: 'Automated Review',
  已开启: 'Enabled',
  未开启: 'Not Enabled',
  店铺短码: 'Shop Short Code',
  系统参考值: 'System Reference',
  继续: 'Continue',
  机审提示: 'Auto-Review Notice',
  system_review_tip1: 'After enabling auto-review, eligible creators’ sample requests will be approved automatically, improving efficiency.',
  system_review_tip2: 'For high-value products needing manual review, turn off auto-review in the "{{content}}" list.',
  Youpik合作商品: 'Youpik Partner Products',
  system_review_enable_extra: 'Once enabled, eligible applications will be <span style="color: red;">auto-approved</span>， improving sample efficiency.',
  模板: 'Template',
  快速出单型: ' Fast Conversion',
  防踩雷保守型: 'Risk-Averse',
  自定义: 'Custom',
  默认条件: 'Default Criteria',
  粉丝量: 'Followers',
  近30天月销: '≥ {{num}} orders in last 30 days',
  出单率: 'Conversion',
  '达人样品申请-发布带货内容-出单计算': 'sample→post→sales',
  履约率: 'Fulfillment',
  '通过达人历史样品申请-发布带货内容计算': 'based on past posts',
  '目标：尽快跑出订单，有明确带货能力': 'Goal: Fast order generation, proven ability',
  '适用场景：新品测试、促销爆量、清库存': 'Use cases: New product launches, promos, clearance',
  '目标：只投信得过、转化能力强的达人': 'Goal: Only trust top performers',
  '适用场景：怕浪费样品，想先从优质达人入手': 'Use case: Avoid wasting samples',
  '请及时补充样品额度，否则达人申样将受影响，最终影响营销效果！': 'Please top up sample quota to avoid impact on creator selling.',
  '补充额度教程': 'Quota Top-Up Guide',
  '确定关闭该活动商品机审': 'Confirm to disable automated review for this promotional product.',
  '商品信息': 'Product Info',
  '剩余样品额度': 'Remaining Sample Quota',
  '机审状态': 'Auto-Review Status',
  '仅展示需补充额度': 'Show only quota needed',
  '商品ID': 'Product ID',
  '商品名称': 'Product Name',
  '所有店铺': 'All Shops',
  'x件可用': '{num} items available',
  '补充额度': 'Top-up Quota',
  '开启中': 'Enabled',
  '关闭': 'Closed',
  '开启': 'Enable',
  '申样限制,需补充额度': 'Sample restricted, {content}',
  '即将用尽,需补充额度': 'Nearly exhausted, {content}',
}
