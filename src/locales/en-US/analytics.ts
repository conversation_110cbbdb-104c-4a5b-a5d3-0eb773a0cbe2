export default {
  统计周期: 'Statistics Period',
  当前投流状态: 'Current Ads Status',
  素材类型: 'Video Type',
  投流中: 'Ads Promoting',
  未投流: 'No Ads',
  已停投: 'Ads Closed',
  纯佣: 'Commission Only',
  纯佣坑位费: 'Commission + Promotion Fee',
  纯佣付费素材: 'Commission + Promotion Fee',
  纯佣素材: 'Commission Only',
  未填写: 'Not Filled',
  视频发布月份: 'Video Release Month',
  Export: 'Export',
  按商品ID: 'Product ID, Video Title, Video ID, or Creator Name.',
  视频信息: 'Video Information',
  达人信息: 'Creator Information',
  效果评估: 'Performance',
  成交信息: 'Transaction Information',
  已投流: 'Ads promoted',
  已关闭: 'Ads closed',
  综合ROI: 'Overall ROI',
  综合GMV: 'Overall GMV',
  发布后7天GMV: 'GMV in 7 Days After Release',
  发布后14天GMV: 'GMV in 14 Days After Release',
  发布后28天GMV: 'GMV in 28 Days After Release',
  综合SKU订单: 'Overall SKU Orders',
  综合销量: 'Overall Sales',
  成本信息: 'Cost Information',
  投流成本: 'Ads Cost',
  坑位费: 'Promotion Fee',
  样品成本: 'Sample Cost',
  请输入样品成本: 'Please Enter Sample Cost',
  视频流量总量: 'Total Video Traffic',
  商品曝光量: 'Product Exposure',
  流量信息: 'Traffic Information',
  商品点击量: 'Product Clicks',
  编辑: 'Edit',
  价格: 'Price',
  ID: 'ID',
  如为纯佣素材: 'If It Is Commission Only Material',
  商品id: 'Product ID',
  视频标题: 'Video Title',
  视频Id: 'Video ID',
  达人名称: 'Creator Name',
  搜索类型: 'Search Type',
  发布时间: 'Release Time',
  视频时长: 'Video Duration',
  商品信息: 'Product Information',
  起始日期: 'Start Date',
  结束日期: 'End Date',
  筛选统计周期:
    'The input-output ratio of this content within the selected statistical period, overall GMV / various input costs. Note: The flow cost is an estimated value based on exposure, not an accurate value, and may cause some deviation in overall ROI, for reference only.',
  筛选统计周期内本条内容创造的GMV:
    'GMV generated by this content during the selected statistical period (non-organic + organic)',
  本条内容发布7天创造的GMV:
    'GMV generated by this content in the first 7 days after publication (non-organic + organic)',
  本条内容发布14天创造的GMV:
    'GMV generated by this content in the first 14 days after publication (non-organic + organic)',
  本条内容发布28天创造的GMV:
    'GMV generated by this content in the first 28 days after publication (non-organic + organic)',
  筛选统计周期内本条内容每个SKU: 'Each SKU of this content during the selected statistical period',
  筛选统计周期内本条内容创造的销售件数:
    'The total number of orders generated by each SKU of this content during the selected statistical period (non-organic + organic)',
  筛选统计周期内通过GMVMax投流花费的总成本:
    'The total cost of GMV Max ad spend during the selected statistical period is allocated to each video based on content exposure share. Note: This is an estimated value, not an accurate value, for reference only.',
  本条内容除了销售佣金以外的单次发布费用:
    'The one-time publishing fee for this content, excluding sales commission, to be filled in by operations.',
  本条内容产生的样品成本:
    'The sample cost generated by this content, including product cost and logistics cost, to be filled in by operations.',
  佣金成本: 'Commission Cost',
  筛选统计周期内本条内容需要支付:
    'Estimated Commission to be Paid to Creator in the Selected Period',
  筛选周期范围内本条内容的曝光量: 'Exposure in Selected Period',
  筛选周期范围内本条内容的商品点击量: 'Product Clicks in Selected Period',
  视频表现: 'Video Performance',
  商品: 'Product',
  商品名称: 'Product Name',
  商品ID: 'Product ID',
  商品价格: 'Product Price',
  单件寄样成本: 'Cost per Sample',
  本次寄样数量: 'Number of Samples This Time',
  设置: 'Settings',
  输入内容不能为空: 'Input Cannot Be Empty',
  本次寄样成本: 'Cost of This Sample',
  样品数量总计: 'Total Sample Quantity',
  样品总计成本: 'Total Sample Cost',
  付费商单: 'Paid Merchant Order',
  YoupikTAP: 'Youpik TAP',
  店铺纯佣: 'Store Pure Commission',
  特殊渠道仅稚优泉: 'Special Channel',
  未分类: 'Unclassified',
  选择店铺: 'Select Shop',
  GMV: 'GMV',
  销量: 'Sales Volume',
  订单: 'Order',
  内容总曝光量: 'Total Content Exposure',
  商品总曝光量: 'Total Product Exposure',
  商品总点击量: 'Total Product Clicks',
  总支出: 'Total Expenditure',
  总坑位费: 'Total Promotion Fee',
  总投流成本: 'Total Ads Cost',
  总联盟佣金: 'Total Affiliate Commission',
  获取投流码: 'Get Ads Code',
  授权TikTok店铺立即解锁: 'Authorize TikTok Shop to unlock immediately',
  解锁视频投流码不用苦苦向达人索取:
    'Unlock video streaming codes without struggling to request from creators',
  解锁ROI分析投入产出一目了然: 'Unlock ROI analysis to see input and output at a glance',
  支持本地跨境店按站点统计无需数据再整合:
    'Support local and cross-border shops with site-specific statistics, no need for data re-integration',
  查看Demo: 'View Demo',
  立即授权: 'Authorize Now',
  当前展示数据罗盘:
    'Currently displaying a Demo with virtual data. To use the Data Compass, please authorize your shop first!',
  当前展示商品:
    'Currently displaying a Demo with virtual data. To use the products, please authorize your shop first!',
  去授权TikTok店铺: 'Go to authorize TikTok Shop >',
  投流码: 'Ads Code',
  gmvTips:
    'GMV generated by this content during the selected statistical period (non-organic + organic)',
  销量Tips:
    'The number of units sold generated by this content during the selected statistical period (non-organic + organic)',
  订单Tips:
    'The total number of orders generated by each SKU of this content during the selected statistical period (non-organic + organic)',
  内容总曝光量Tips: 'Exposure in Selected Period',
  商品总曝光量Tips: 'Exposure in Selected Period',
  商品总点击量Tips: 'Product Clicks in Selected Period',
  总坑位费Tips:
    'The one-time publishing fee for this content, excluding sales commission, to be filled in by operations.',
  总投流成本Tips:
    'The total cost of GMV Max ad spend during the selected statistical period is allocated to each video based on content exposure share. Note: This is an estimated value, not an accurate value, for reference only.',
  总联盟佣金Tips:
    'The estimated total commission to be paid to the creator within the selected statistical period, including cash on delivery (paid and unpaid) and non-cash on delivery orders, as well as refunds.',
  总支出Tips:
    'The sample cost generated by this content, including product cost and logistics cost, to be filled in by operations.',
  展开面板: 'Expand Panel ↓',
  收起面板: 'Collapse Panel ↑',
  有投流码: 'Has Ads Code',
  无投流码: 'No Ads Code',
  '店铺纯佣(公开/定向)': 'Store Pure Commission (Open/Targeted)',
  '特殊渠道（仅稚优泉特定tap申样标签）':
    " Special Channel (Exclusive to youzhiquan's TAP Sample Request Tag)"
}
