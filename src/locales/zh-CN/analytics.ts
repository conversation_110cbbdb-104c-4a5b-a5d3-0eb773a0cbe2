export default {
  统计周期: '统计周期',
  当前投流状态: '当前投流状态',
  素材类型: '素材类型',
  投流中: '投流中',
  未投流: '未投流',
  已停投: '已停投',
  纯佣: '纯佣',
  纯佣坑位费: '纯佣+坑位费',
  纯佣付费素材: '纯佣+付费素材',
  纯佣素材: '纯佣素材',
  未填写: '未填写',
  视频发布月份: '视频发布月份',
  Export: 'Export',
  按商品ID: '按商品ID、视频标题、视频ID或达人名称搜索',
  视频信息: '视频信息',
  达人信息: '达人信息',
  效果评估: '效果评估',
  成交信息: '成交信息',
  已投流: '已投流',
  已关闭: '已关闭',
  综合ROI: '综合ROI',
  综合GMV: '综合GMV',
  发布后7天GMV: '发布后7天GMV',
  发布后14天GMV: '发布后14天GMV',
  发布后28天GMV: '发布后28天GMV',
  综合SKU订单: '综合SKU订单',
  综合销量: '综合销量',
  成本信息: '成本信息',
  投流成本: '投流成本',
  坑位费: '坑位费',
  样品成本: '样品成本',
  请输入样品成本: '请输入样品成本',
  视频流量总量: '视频流量总量',
  商品曝光量: '商品曝光量',
  流量信息: '流量信息',
  商品点击量: '商品点击量',
  //   广告CTR: '广告CTR%',
  //   广告转化: '广告转化%',
  编辑: '编辑',
  价格: '价格',
  ID: 'ID',
  如为纯佣素材: '如为纯佣素材，请标记，无需输入坑位费',
  商品id: '商品 id',
  视频标题: '视频标题',
  视频Id: '视频 Id',
  达人名称: '达人名称',
  搜索类型: '搜索类型',
  发布时间: '发布时间',
  视频时长: '视频时长',
  商品信息: '商品信息',
  起始日期: '起始日期',
  结束日期: '结束日期',
  筛选统计周期:
    '筛选统计周期内本条内容的投入产出比，综合GMV/各项投入成本，注：其中投流成本项为按曝光折算成本的预估值，非准确值，对综合ROI可能造成一定偏差，仅供参考。',
  筛选统计周期内本条内容创造的GMV: '筛选统计周期内本条内容创造的GMV（非自然+自然）',
  本条内容发布7天创造的GMV: '本条内容发布7天创造的GMV（非自然+自然）',
  本条内容发布14天创造的GMV: '本条内容发布14天创造的GMV（非自然+自然）',
  本条内容发布28天创造的GMV: '本条内容发布28天创造的GMV（非自然+自然）',
  筛选统计周期内本条内容每个SKU:
    '筛选统计周期内本条内容每个SKU对应产生的订单数数之和（非自然+自然）',
  筛选统计周期内本条内容创造的销售件数: '筛选统计周期内本条内容创造的销售件数（非自然+自然）',
  筛选统计周期内通过GMVMax投流花费的总成本:
    '筛选统计周期内通过GMV Max投流花费的总成本按内容曝光占比摊到每条视频上。注：此为预估值，非准确值，仅供参考。',
  本条内容除了销售佣金以外的单次发布费用: '本条内容除了销售佣金以外的单次发布费用，由运营填写',
  本条内容产生的样品成本: '本条内容产生的样品成本，包括产品成本和物流成本等，由运营填写',
  佣金成本: '佣金成本',
  筛选统计周期内本条内容需要支付:
    '筛选统计周期内本条内容需要支付给达人的预计佣金总金额，包括货到付款（已付款和未付款）和非货到付款订单,以及退款',
  筛选周期范围内本条内容的曝光量: '筛选周期范围内本条内容的曝光量',
  筛选周期范围内本条内容的商品点击量: '筛选周期范围内本条内容的商品点击量',
  视频表现: '视频表现',
  商品: '商品',
  商品名称: '商品名称',
  商品ID: '商品ID',
  商品价格: '商品价格',
  单件寄样成本: '单件寄样成本',
  本次寄样数量: '本次寄样数量',
  设置: '设置',
  输入内容不能为空: '输入内容不能为空',
  本次寄样成本: '本次寄样成本',
  样品数量总计: '样品数量总计',
  样品总计成本: '样品总计成本',
  付费商单: '付费商单',
  YoupikTAP: 'Youpik TAP',
  店铺纯佣: '店铺纯佣',
  特殊渠道仅稚优泉: '特殊渠道仅稚优泉',
  未分类: '未分类',
  选择店铺: '选择店铺',
  GMV: 'GMV',
  销量: '销量',
  订单: '订单',
  内容总曝光量: '内容总曝光量',
  商品总曝光量: '商品总曝光量',
  商品总点击量: '商品总点击量',
  总支出: '总支出',
  总坑位费: '总坑位费',
  总投流成本: '总投流成本',
  总联盟佣金: '总联盟佣金',
  获取投流码: '获取投流码',
  授权TikTok店铺立即解锁: '授权TikTok店铺,立即解锁',
  解锁视频投流码不用苦苦向达人索取: '解锁视频投流码，不用苦苦向达人索取',
  解锁ROI分析投入产出一目了然: '解锁ROI分析，投入产出一目了然',
  支持本地跨境店按站点统计无需数据再整合: '支持本地、跨境店按站点统计，无需数据再整合',
  查看Demo: '查看Demo',
  立即授权: '立即授权',
  当前展示数据罗盘: '当前展示为Demo演示，均为虚拟数据，若要使用数据罗盘，请先授权店铺！',
  当前展示商品: '当前展示为Demo演示，均为虚拟数据，若要使用商品，请先授权店铺！',
  去授权TikTok店铺: '去授权TikTok店铺>',
  投流码: '投流码',
  gmvTips: '筛选统计周期内本条内容创造的GMV之和（非自然+自然）',
  销量Tips: '筛选统计周期内本条内容创造的销售件数之和（非自然+自然)',
  订单Tips: '筛选统计周期内本条内容每个SKU对应产生的订单数数之和（非自然+自然）',
  内容总曝光量Tips: '筛选周期范围内本条内容的曝光量之和',
  商品总曝光量Tips: '筛选周期范围内本条内容的曝光量之和',
  商品总点击量Tips: '筛选周期范围内本条内容的商品点击量之和',
  总坑位费Tips: '本条内容除了销售佣金以外的单次发布费用，由运营填写',
  总投流成本Tips:
    '筛选统计周期内通过GMV Max投流花费的总成本按内容曝光占比摊到每条视频上。注：此为预估值，非准确值，仅供参考。',
  总联盟佣金Tips:
    '筛选统计周期内本条内容需要支付给达人的预计佣金总金额，包括货到付款（已付款和未付款）和非货到付款订单,以及退款',
  总支出Tips: '本条内容产生的样品成本，包括产品成本和物流成本等，由运营填写',
  展开面板: '更多面板 ↓',
  收起面板: '收起面板 ↑',
  有投流码: '有投流码',
  无投流码: '无投流码',
  '店铺纯佣(公开/定向)': '店铺纯佣(公开/定向)',
  '特殊渠道（仅稚优泉特定tap申样标签）': '特殊渠道（仅稚优泉特定tap申样标签）',
}
