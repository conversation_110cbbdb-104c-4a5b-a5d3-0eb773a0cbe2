import { getItemConfig } from '@/api/config'
import { useRequest } from 'ahooks'
import { useState } from 'react'

const fetctItemConfig = async () => {
  try {
    const { result } = await getItemConfig()
    return Promise.resolve(result)
  } catch (error) {
    return Promise.resolve(null)
  }
}

const useConfigModel = () => {
  const [config, setConfig] = useState<API.ChoiceItemConfig>()

  const { loading, runAsync: fetch } = useRequest(fetctItemConfig, {
    manual: true,
    loadingDelay: 300,
    onSuccess(data) {
      setConfig(data ?? undefined)
    }
  })

  return { loading, config, fetch, setConfig }
}

export default useConfigModel
