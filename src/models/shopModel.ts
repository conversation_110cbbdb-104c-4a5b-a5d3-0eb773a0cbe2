/*
 * @Author: <PERSON>
 * @Date: 2024-04-26 16:23:11
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2024-04-30 14:31:56
 * @Desc 店铺全局状态
 */
import { getShopList } from '@/api/product'
import { useState } from 'react'

const useShopModel = () => {
  const [shopList, setShopList] = useState<API.TikTokShop[]>([])

  const fetch = async () => {
    if (shopList.length) return
    try {
      const { result = [] } = await getShopList()
      setShopList(result)
    } catch (error) {
      console.log(error)
    }
  }

  return { shopList, fetch }
}

export default useShopModel
