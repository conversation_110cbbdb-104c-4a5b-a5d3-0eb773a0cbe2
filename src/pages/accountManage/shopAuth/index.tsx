import React, { useEffect, useState } from 'react'
import { Card, Table, Space, Button, message } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useIntl } from 'umi'
import { formatNumber } from '@/utils/common'
import shopApi from '@/api/shop'
import { useQuery } from '@/hooks'
import { formatDateTime } from '@/utils/format'
import useShopAuthLock from '@/hooks/useShopAuthLock'
import styles from './index.less'
import useSampleReview from '@/hooks/useSampleReview'

function closeParentPage() {
  if (window.opener) {
    window.opener.close() // 关闭原页面
  }
}

const ShopAuth = () => {
  const intl = useIntl()
  const [dataSource, setDataSource] = useState<any>([])
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [currentRecord, setCurrentRecord] = useState<any>()
  const [authDisabled, setAuthDisabled] = useState<boolean>(false)
  const query = useQuery()

  useShopAuthLock()
  useSampleReview()

  useEffect(() => {
    getDataSource()
    const { status, errorMsg } = query
    if (status === '0' && errorMsg) {
      message.error(errorMsg)
      window.history.replaceState(null, '', window.location.pathname)
      closeParentPage()
    }
    if (status === '1') {
      message.success(intl.formatMessage({ id: 'locale.授权成功' }))
      window.history.replaceState(null, '', window.location.pathname)
      closeParentPage()
    }
  }, [])

  const getDataSource = async () => {
    setFetchLoading(true)
    try {
      const params = {}
      const result = await shopApi.getAuthedTiktokAccountList(params)
      if (result.code == 200 && result.result) {
        setDataSource(result.result)
      }
      setFetchLoading(false)
    } catch (error) {
      setFetchLoading(false)
    }
  }

  const columns: ColumnsType<any> = [
    {
      title: intl.formatMessage({ id: 'account.TikTok店铺' }),
      key: 'firstColumn',
      align: 'center',
      render(value, record, index) {
        return (
          <div className={styles.shop_info_container}>
            {/* <img className={styles.shop_image} src={record.authUrl}></img> */}
            <div className={styles.shop_content_container}>
              <div className={styles.shop_name}>{record.shopName}</div>
              <div className={styles.shop_code}>{`${intl.formatMessage({
                id: 'account.店铺短码'
              })}:${record.shopCode}`}</div>
            </div>
          </div>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'account.店铺类型' }),
      key: 'shopType',
      align: 'center',
      render: (value, record, index) => {
        const { sellerType } = record // CROSS_BORDER LOCAL
        if (!sellerType) {
          return '-'
        }

        return sellerType === 'CROSS_BORDER'
          ? intl.formatMessage({ id: 'account.跨境店铺(泰国越南)' })
          : intl.formatMessage({ id: 'account.本地店铺(泰国)' })
      }
    },
    {
      title: intl.formatMessage({ id: 'account.授权状态' }),
      key: 'authStatus',
      align: 'center',
      render: (value, record, index) => {
        const { authStatus } = record
        let authColor = '#009995'
        if (authStatus === 0) {
          authColor = 'red'
        } else if (authStatus === 1) {
          authColor = '#009995'
        } else if (authStatus === 2) {
          authColor = 'orange'
        }

        let authName = 'locale.已授权'
        if (authStatus === 0) {
          authName = 'locale.未授权'
        } else if (authStatus === 1) {
          authName = 'locale.已授权'
        } else if (authStatus === 2) {
          authName = 'locale.授权到期'
        }

        return (
          <div className={styles.auth_status_container}>
            <div className={styles.auth_status_dot} style={{ backgroundColor: authColor }} />
            <div style={{ color: authStatus === 1 ? '#009995' : '#999' }}>
              {intl.formatMessage({ id: authName })}
            </div>
          </div>
        )
      }
    },
    // {
    //   title: intl.formatMessage({ id: 'locale.授权过期时间' }),
    //   key: 'refreshTokenExpireIn',
    //   align: 'center',
    //   render: (value, record, index) => {
    //     const { refreshTokenExpireIn } = record
    //     return refreshTokenExpireIn ? formatDateTime(refreshTokenExpireIn) : '-'
    //   }
    // },
    {
      title: intl.formatMessage({ id: 'account.操作' }),
      key: 'action',
      align: 'center',
      render: (value, record, index) => {
        const { authStatus } = record
        return authStatus === 0 || authStatus === 2 ? (
          <a href="" onClick={(e) => handleQuickBind(record, e)}>
            {intl.formatMessage({ id: 'account.立即绑定' })}
          </a>
        ) : (
          '-'
        )
      }
    }
  ]

  const handleUnbind = (record: any, e: any) => {
    e.preventDefault()
    if (authDisabled) {
      return
    }
    setCurrentRecord(record)
    handleAuth()
  }

  const handleQuickBind = (record: any, e: any) => {
    e.preventDefault()
    if (authDisabled) {
      return
    }
    setCurrentRecord(record)
    handleAuth()
  }

  const handleAddShopAuth = () => {
    handleAuth()
  }

  const handleAuth = async () => {
    try {
      setAuthDisabled(true)
      const result = await shopApi.getTiktokAuthUrl('/accountManage/shopAuth')
      setAuthDisabled(false)
      if (result.code == 200 && result.result) {
        window.open(result.result, '_blank')
      }
    } catch (error) {
      setAuthDisabled(false)
    }
  }

  return (
    <>
      <div className={styles.header_container}>
        <div className={styles.header}> {intl.formatMessage({ id: 'account.TikTok店铺授权' })}</div>
        <Button type="primary" onClick={handleAddShopAuth} disabled={authDisabled}>
          {intl.formatMessage({ id: 'account.授权店铺' })}
        </Button>
      </div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card className={`${styles['m-list']}`} bordered={false}>
          <Table
            rowKey="id"
            columns={columns}
            dataSource={dataSource}
            loading={fetchLoading}
            pagination={false}
          />
        </Card>
        <Card title={intl.formatMessage({ id: 'account.店铺授权须知' })} bordered={false}>
          <ul>
            <li>
              {intl.formatMessage({
                id: 'account.店铺授权后可查看联盟带货达人的数据效果获取素材adscode'
              })}
            </li>
            <li>
              {intl.formatMessage({
                id: 'account.Youpik承诺保护商家的店铺数据安全所有店铺仅用于帮助商家数据分析使用'
              })}
            </li>
          </ul>
        </Card>
      </Space>
    </>
  )
}

export default ShopAuth
