import React, { useEffect, useMemo, useRef, useState } from 'react'
import { history, useIntl, getLocale } from 'umi'
import { intlTrans } from '@/utils/common'
import withPageTitle from '@/HOCs/withPageTitle'
import {
  Alert,
  Button,
  Card,
  Space,
  Checkbox,
  Image,
  Tooltip,
  Badge,
  Modal,
  Input,
  Select,
  message
} from 'antd'
import { ActionType, idIDIntl, ProTable } from '@ant-design/pro-components'
import { ExclamationCircleFilled, ExclamationCircleOutlined } from '@ant-design/icons'
import { CheckboxChangeEvent } from 'antd/lib/checkbox'
import { formatMoney, formatNum, formatPrice } from '@/utils/format'
import { suppliersShops } from '@/api/sample'
import { getYoupikOperateProducts, auditYoupikOperateProduct } from '@/api/product'
import Copy from '@/components/Copy'
import styles from './index.less'

const { confirm } = Modal

const guidePdfZh =
  'https://file-v2.uchoice.pro/common/uchoice/sample-quantity-for-online-shipping_zhch.pdf'
const guidePdfEn =
  'https://file-v2.uchoice.pro/common/uchoice/sample-quantity-for-online-shipping.pdf'
const guidePdfTh =
  'https://file-v2.uchoice.pro/common/uchoice/sample-quantity-for-online-shipping_th.pdf'
const guidePdfVi =
  'https://file-v2.uchoice.pro/common/uchoice/sample-quantity-for-online-shipping_vn.pdf'

const YoupikCooperate = () => {
  const actionRef = useRef<ActionType | undefined>(undefined)
  const [quotaChecked, setQuotaChecked] = useState(false)
  const quotaCheckedRef = useRef<boolean>(false)
  const actionLoadingRef = useRef<boolean>(false)
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const intl = useIntl()
  const language = getLocale()

  const [shopLoading, setShopLoading] = useState<boolean>(false)
  const [supplierShops, setSupplierShops] = useState<any[]>([])

  const guidePdf = useMemo(() => {
    if (language === 'zh-CN') {
      return guidePdfZh
    } else if (language === 'en-US') {
      return guidePdfEn
    } else if (language === 'th-TH') {
      return guidePdfTh
    } else if (language === 'vi-VN') {
      return guidePdfVi
    }
    return guidePdfEn
  }, [language])

  const getShops = async () => {
    try {
      setShopLoading(true)
      const res = await suppliersShops()
      if (res.code === 200) {
        const value = res.result.map((item: any) => {
          return {
            label: item.shopName,
            value: item.shopCode
          }
        })
        setSupplierShops(value)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setShopLoading(false)
    }
  }

  useEffect(() => {
    getShops()
  }, [])

  const handleMachineAuditClose = (record: any, e: any) => {
    e.preventDefault()
    if (actionLoadingRef.current) {
      return
    }
    confirm({
      title: intl.formatMessage({ id: 'account.确定关闭该活动商品机审' }),
      icon: <ExclamationCircleFilled />,
      content: (
        <div
          dangerouslySetInnerHTML={{
            __html: intl.formatMessage({ id: 'account.关闭后所有样品将由商家手动审核你还要继续吗' })
          }}
        ></div>
      ),
      okText: intl.formatMessage({ id: 'account.确定' }),
      cancelText: intl.formatMessage({ id: 'account.取消' }),
      onOk() {
        actionLoadingRef.current = true
        auditYoupikOperateProduct(record.id, '0')
          .then(() => {
            message.success(intl.formatMessage({ id: 'common.OperationSuccessful' }))
            actionRef.current?.reload()
          })
          .finally(() => {
            actionLoadingRef.current = false
          })
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }

  const handleMachineAuditOpen = (record: any, e: any) => {
    e.preventDefault()
    if (actionLoadingRef.current) {
      return
    }
    const { auditStatus } = record
    if (auditStatus === 2) {
      history.push('/machineReview')
    }
    if (auditStatus === 0) {
      actionLoadingRef.current = true
      auditYoupikOperateProduct(record.id, '1')
        .then(() => {
          message.success(intl.formatMessage({ id: 'common.OperationSuccessful' }))
          actionRef.current?.reload()
        })
        .finally(() => {
          actionLoadingRef.current = false
        })
    }
  }

  const columns = [
    {
      title: intl.formatMessage({ id: 'account.商品信息' }),
      dataIndex: 'productInfo',
      hideInSearch: true,
      width: 412,
      render: (text: any, record: any) => {
        return (
          <div className={styles.product_container}>
            <Image src={record.image} preview className={styles.product_thumbnail}></Image>
            <div className={styles.product_info}>
              <Tooltip title={record.productName || ''}>
                <div className={styles.product_name}>{record.productName || ''}</div>
              </Tooltip>
              <div className={styles.product_id_container}>
                <div className={styles.product_id}>
                  {intlTrans('account.商品ID')}: {record.productId || ''}
                </div>
              </div>
              <div className={styles.price}>{`${
                record.minPrice == record.maxPrice
                  ? formatPrice(record.minPrice, true)
                  : `${formatPrice(record.minPrice, true)} - ${formatPrice(record.maxPrice, true)}`
              }`}</div>
            </div>
          </div>
        )
      }
    },
    {
      title: intlTrans('account.剩余样品额度'),
      dataIndex: 'sampleQuota',
      hideInSearch: true,
      render: (text: any, record: any) => {
        const { sampleQuota, campaignLink } = record
        return (
          <div className={styles.quota_container}>
            <div
              dangerouslySetInnerHTML={{
                __html: intl.formatMessage(
                  { id: 'account.x件可用' },
                  {
                    num: `<span style="color: ${
                      sampleQuota === 0 ? 'red' : '#303030'
                    };">${formatNum(sampleQuota)}</span>`
                  }
                )
              }}
            ></div>
            <div>
              {sampleQuota > 30 && (
                <a style={{ marginTop: 4 }} href={campaignLink} target="_blank">
                  {intlTrans('account.补充额度')}
                </a>
              )}
              {sampleQuota === 0 && (
                <span style={{ marginTop: 4 }}>
                  <ExclamationCircleOutlined
                    style={{ color: 'orange', marginRight: 4, fontSize: 12 }}
                  />
                  <span
                    dangerouslySetInnerHTML={{
                      __html: intl.formatMessage(
                        { id: 'account.申样限制,需补充额度' },
                        {
                          content: `<a href="${campaignLink}" target="_blank">${intlTrans(
                            'account.补充额度'
                          )}</a>`
                        }
                      )
                    }}
                  ></span>
                </span>
              )}
              {sampleQuota <= 30 && sampleQuota > 0 && (
                <span style={{ marginTop: 4 }}>
                  <ExclamationCircleOutlined
                    style={{ color: 'orange', marginRight: 4, fontSize: 12 }}
                  />
                  <span
                    dangerouslySetInnerHTML={{
                      __html: intl.formatMessage(
                        { id: 'account.即将用尽,需补充额度' },
                        {
                          content: `<a href="${campaignLink}" target="_blank">${intlTrans(
                            'account.补充额度'
                          )}</a>`
                        }
                      )
                    }}
                  ></span>
                </span>
              )}
              {campaignLink && <Copy text={campaignLink} />}
            </div>
          </div>
        )
      }
    },
    {
      title: intlTrans('account.机审状态'),
      dataIndex: 'auditStatus',
      hideInSearch: true,
      render: (text: any, record: any) => {
        const { auditStatus } = record
        return auditStatus === 1 ? (
          <div className={styles.machine_audit_container}>
            <Badge color="lime" text={intlTrans('account.开启中')} />
            <a
              style={{ color: 'gray', marginTop: 4 }}
              onClick={(e) => handleMachineAuditClose(record, e)}
            >
              {intlTrans('account.关闭')}
            </a>
          </div>
        ) : (
          <div className={styles.machine_audit_container}>
            <Badge color="gray" text={intlTrans('account.未开启')} />
            <a style={{ marginTop: 4 }} onClick={(e) => handleMachineAuditOpen(record, e)}>
              {intlTrans('account.开启')}
            </a>
          </div>
        )
      }
    },
    {
      title: intlTrans('account.商品ID'),
      dataIndex: 'productId',
      hideInTable: true,
      renderFormItem: (config: any) => {
        return <Input placeholder={intlTrans('common.PleaseEnter')} />
      }
    },
    {
      title: intlTrans('account.商品名称'),
      dataIndex: 'productName',
      hideInTable: true,
      renderFormItem: (config: any) => {
        return <Input placeholder={intlTrans('common.PleaseEnter')} />
      }
    },
    {
      title: intlTrans('sample.店铺'),
      dataIndex: 'shop',
      hideInTable: true,
      renderFormItem: (schema: any, config: any, form: any, action: any) => {
        return (
          <Select
            {...config.fieldProps}
            style={{ width: '150px' }}
            options={supplierShops}
            dropdownStyle={{ zIndex: 99 }}
            allowClear
          />
        )
      },
      formItemProps: {
        label: intlTrans('sample.店铺'),
        name: 'shopCode'
      },
      fieldProps: {
        placeholder: intlTrans('common.PleaseSelect'),
        loading: shopLoading
      }
    }
  ]

  const onRequest = async (params: any, sort: any, filter: any) => {
    const { current, pageSize, productId, productName, shopCode } = params
    try {
      setFetchLoading(true)
      const params: any = {
        pageNo: current,
        pageSize
      }
      if (productId) {
        params.productId = productId
      }
      if (productName) {
        params.productName = productName
      }
      if (shopCode) {
        params.shopCode = shopCode
      }
      params.lowSampleQuota = quotaCheckedRef.current
      const res = await getYoupikOperateProducts(params)
      if (res.code === 200 && res.result && res.result.list) {
        const { total, list } = res.result
        return {
          data: list,
          success: true,
          total: total
        }
      }
      return {
        data: [],
        success: true,
        total: 0
      }
    } catch (error) {
      return {
        data: [],
        success: true,
        total: 0
      }
    } finally {
      setFetchLoading(false)
    }
  }

  const handleToGuide = () => {}

  const onCheckboxChange = (e: CheckboxChangeEvent) => {
    setQuotaChecked(e.target.checked)
    quotaCheckedRef.current = e.target.checked
    actionRef.current?.reloadAndRest?.()
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Alert
        showIcon
        message={intlTrans('account.请及时补充样品额度，否则达人申样将受影响，最终影响营销效果！')}
        action={
          <a href={guidePdf} target="_blank">
            {intlTrans('account.补充额度教程')}
          </a>
        }
      />
      <Card>
        <ProTable<any>
          actionRef={actionRef}
          columns={columns}
          request={onRequest}
          headerTitle={
            <Checkbox disabled={fetchLoading} checked={quotaChecked} onChange={onCheckboxChange}>
              {intlTrans('account.仅展示需补充额度')}
            </Checkbox>
          }
          options={false}
          search={{
            labelWidth: 'auto',
            searchText: intlTrans('common.Search'),
            resetText: intlTrans('common.Reset'),
            collapseRender: () => null,
            collapsed: false
          }}
          pagination={{
            size: 'small',
            defaultPageSize: 10,
            showQuickJumper: true,
            showSizeChanger: true,
            position: ['bottomRight'],
            showTotal(total) {
              return `${intlTrans('common.Total')} ${total}`
            }
          }}
        />
      </Card>
    </Space>
  )
}

export default withPageTitle(YoupikCooperate, intlTrans('account.Youpik合作商品'))
