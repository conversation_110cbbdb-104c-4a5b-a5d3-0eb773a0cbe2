import styles from './index.less'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { intlTrans, replaceExceptNumber } from '@/utils/common'
import { Button, Form, Input, InputNumber, message, Modal, Select } from 'antd'
import NumberInput from '@/components/NumberInput'
import { formatMoney } from '@/utils/format'
import { setSampleCost } from '@/api/product'

interface Props {
  isEditModalOpen: boolean
  handleOk: (e: any) => void
  handleCancel: () => void

  item: any
}

export default function EditModal({ isEditModalOpen, handleOk, handleCancel, item }: Props) {
  const [form] = Form.useForm()
  const [confirmBtnViable, setConfirmBtnViable] = useState(false)
  const handleClickOk = () => {
    form.validateFields().then((values) => {
      setConfirmBtnViable(true)
      values.productId = item?.productId
      values.shopCode = item?.shopCode
      if (!values.sampleCost) {
        values.sampleCost = 0
      }
      setSampleCost({ ...values })
        .then((res) => {
          if (res.code === 200 && res.result) {
            message.success(intlTrans('common.OperationSuccessful'))
            form.resetFields()
          } else {
            message.error(intlTrans('common.OperationFailed'))
          }
          handleOk(values)
          setConfirmBtnViable(false)
        })
        .catch((errorInfo) => {
          console.log(errorInfo)
          setConfirmBtnViable(false)
        })
    })
  }
  useEffect(() => {
    if (item) {
      form.setFieldValue('sampleCost', parseFloat(item?.sampleCost).toFixed(2))
    }
  }, [item, form])
  return (
    <Modal
      width="600px"
      title={<div className={styles.modal_box}>{intlTrans('commodity.单件寄样成本')}</div>}
      open={isEditModalOpen}
      destroyOnClose
      cancelText={intlTrans('common.Cancel')}
      okText={intlTrans('common.Sure')}
      onOk={handleClickOk}
      onCancel={() => {
        handleCancel()
        if (item) {
          form.setFieldValue('sampleCost', parseFloat(item?.sampleCost).toFixed(2))
        }
      }}
      confirmLoading={confirmBtnViable}
    >
      <div className={styles.single_item}>
        <div className={styles.single_item_img}>
          <img className={styles.single_item_img} src={item?.mainImage} alt="" />
        </div>
        <div className={styles.product_boxs}>
          <div className={styles.product_box}>
            <div className={styles.product_label}>{intlTrans('analytics.商品名称')}:</div>
            <div className={styles.product_title}>{item.productTitle}</div>
          </div>
          <div className={styles.product_box} style={{ marginTop: '10px' }}>
            <div className={styles.product_label}>{intlTrans('analytics.商品ID')}:</div>
            <div className={styles.product_title}>{item.productId || '-'}</div>
          </div>
          <div className={styles.product_box} style={{ marginTop: '10px' }}>
            <div className={styles.product_label}>{intlTrans('analytics.商品价格')}:</div>
            <div className={styles.product_title}>
              {item.minPrice || item.minPrice === 0 ? (
                <span>{`${
                  item.minPrice == item.maxPrice
                    ? formatMoney(item.minPrice, true)
                    : `${formatMoney(item.minPrice, true)} - ${formatMoney(item.maxPrice, true)}`
                }`}</span>
              ) : (
                <div className={styles.center_box}>-</div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Form
        form={form}
        labelAlign="left"
        name="form"
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 18 }}
      >
        <Form.Item
          label={intlTrans('commodity.单件寄样成本')}
          name="sampleCost"
          className={styles.input_box}
          rules={[{ required: true, message: intlTrans('commodity.输入内容不能为空') }]}
        >
          {/* <InputNumber addonBefore="฿" precision={2} /> */}
          <NumberInput
            autoComplete="off"
            replenishPoint
            onChange={() => {}}
            addonBefore="฿"
            onBlur={() => {
              const sampleCost = form.getFieldValue('sampleCost')
              if (!isNaN(sampleCost) && sampleCost !== '') {
                // 如果值有效，格式化为两位小数
                form.setFieldsValue({
                  sampleCost: parseFloat(sampleCost).toFixed(2)
                })
              } else {
                // 如果值无效，清空
                form.setFieldsValue({
                  sampleCost: ''
                })
              }
            }}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" />
        </Form.Item>
      </Form>
    </Modal>
  )
}
