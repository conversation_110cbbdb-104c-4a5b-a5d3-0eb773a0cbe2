import withPageTitle from '@/HOCs/withPageTitle'
import ProductImage from '@/components/ProductImage'
import Table from '@/components/Table'
import useTableRequest from '@/hooks/useTableRequest'
import { intlTrans } from '@/utils/common'
import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components'
import { useBoolean } from 'ahooks'
import { Button, Spin, Select, Input, Tooltip, Alert, message } from 'antd'
import { useMemo, useRef, useState, useEffect } from 'react'
import styles from './index.less'
import { formatMoney, formatPrice } from '@/utils/format'
import 'moment-timezone'
import ErrorImage from '@/components/ErrorImage'
import { getProductList, productListMock } from '@/api/product'
import { SearchOutlined } from '@ant-design/icons'
import Copy from '@/components/Copy'
import { useQuery } from '@/hooks'
import EditModal from '../EditModal'
import shopApi from '@/api/shop'
import { suppliersShops } from '@/api/sample'
import { validShops } from '@/api/analytics'

const ManageContent = () => {
  const [isEditModalOpen, setEditModalOpen] = useState(false)
  const [loading, { toggle: loadingToggle }] = useBoolean()
  const [editValue, setEditValue] = useState<any>({})
  const actionRef = useRef<ActionType>()
  const formRef = useRef<FormInstance>()
  const queryParams = useQuery()
  const isDemoRef = useRef(false)
  const [supplierShops, setSupplierShops] = useState<any>([])
  const supplierShopsRef = useRef()
  const [isDemo, setIsDemo] = useState(false)
  const [authDisabled, setAuthDisabled] = useState(false)
  const query = useQuery()
  function closeParentPage() {
    if (window.opener) {
      window.opener.close() // 关闭原页面
    }
  }
  const handleShop = async () => {
    const res = await validShops()
    if (res.code === 200) {
      const value = res.result.map((item: any) => {
        return {
          label: item.shopName,
          value: item.shopCode
        }
      })
      setSupplierShops(value)
      supplierShopsRef.current = value
    }
  }

  useEffect(() => {
    const { status, errorMsg, isDemo } = queryParams
    let url = window.location.pathname
    if (isDemo) {
      url = window.location.pathname + '?isDemo=true'
    }
    if (status === '0' && errorMsg) {
      message.error(errorMsg)
      window.history.replaceState(null, '', url)
      closeParentPage()
    }
    if (status === '1') {
      message.success(intlTrans('locale.授权成功'))
      window.history.replaceState(null, '', url)
      closeParentPage()
    }
    handleShop()
  }, [supplierShopsRef.current])
  useEffect(() => {
    if (queryParams && queryParams.isDemo) {
      setIsDemo(queryParams.isDemo)
      isDemoRef.current = queryParams.isDemo
    }
    if (!formRef.current) {
      return
    }

    if (queryParams && queryParams.productId) {
      formRef.current.setFieldValue('param', queryParams.productId)

      formRef.current?.submit()
    }
  }, [])
  const handleToTiktokShop = async () => {
    try {
      setAuthDisabled(true)
      const result = await shopApi.getTiktokAuthUrl('/commodity/manage?isDemo=true')
      setAuthDisabled(false)
      if (result.code == 200 && result.result) {
        window.open(result.result, '_blank')
      }
    } catch (error) {
      setAuthDisabled(false)
    }
  }
  const onRequest = useTableRequest(async (params) => {
    try {
      const res = isDemo ? await productListMock(params) : await getProductList(params)
      return Promise.resolve(res)
    } catch (error) {
      return Promise.reject(error)
    }
  })

  //点击编辑按钮触发
  const handleClickEdit = (item: any) => {
    setEditModalOpen(true)

    setEditValue(item)
  }
  const columns = useMemo<ProColumns<any>[]>(() => {
    return [
      // 商品信息
      {
        title: intlTrans('commodity.商品'),
        dataIndex: 'mainImage',
        fixed: true,
        order: 98,
        align: 'center',
        colSize: 2,
        width: '150px',
        renderText(text, item) {
          return (
            <>
              {item.productTitle && item.minPrice && item.productId ? (
                <div className={styles.item_box}>
                  <div>{text ? <ProductImage src={text} /> : <ErrorImage />}</div>
                  <div className={styles.item_content}>
                    <div className={styles.title_box}>
                      <Tooltip title={item.productTitle}>
                        <span className={styles.item_name}>{item.productTitle || '-'}</span>
                      </Tooltip>
                    </div>

                    <div className={styles.video_id}>
                      {intlTrans('analytics.ID')}:{item.productId || '-'}
                      <Copy text={item.productId} />
                    </div>
                  </div>
                </div>
              ) : (
                '-'
              )}
            </>
          )
        },
        //搜索商品id
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <Input
              {...config.fieldProps}
              allowClear
              suffix={
                <SearchOutlined
                  onClick={() => {
                    formRef.current?.submit()
                  }}
                />
              }
            />
          )
        },
        formItemProps: {
          label: '',
          name: 'param'
        },
        fieldProps: {
          placeholder: intlTrans('commodity.按商品ID商品名称搜索')
        }
      },
      //价格
      {
        title: intlTrans('commodity.价格'),
        dataIndex: 'avatar',
        align: 'center',
        fixed: true,
        order: 96,
        colSize: 1.2,
        renderText(text, item) {
          return (
            <div className={styles.master_box}>
              {item.minPrice || item.minPrice === 0 ? (
                <span>{`${
                  item.minPrice == item.maxPrice
                    ? formatPrice(item.minPrice, true)
                    : `${formatPrice(item.minPrice, true)} - ${formatPrice(item.maxPrice, true)}`
                }`}</span>
              ) : (
                <div className={styles.center_box}>-</div>
              )}
            </div>
          )
        },
        //单件寄样成本
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <Select
              {...config.fieldProps}
              defaultValue={'0'}
              style={{ width: '150px' }}
              dropdownStyle={{ zIndex: 99 }}
            />
          )
        },
        formItemProps: {
          label: intlTrans('commodity.单件寄样成本'),
          name: 'sampleCost'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseEnter'),
          options: [
            { label: intlTrans('common.All'), value: '0' },
            { label: intlTrans('commodity.未设置'), value: '1' },
            { label: intlTrans('commodity.已设置'), value: '2' }
          ]
        }
      },

      // 单件寄样成本
      {
        title: intlTrans('commodity.单件寄样成本'),
        dataIndex: 'sampleCost',
        align: 'center',
        width: 150,
        order: 95,
        colSize: 1,
        renderText(text, item) {
          return (
            <div className={styles.master_box}>
              <div>{formatPrice(text, true)}</div>
              <div className={styles.master_content}>
                <Button
                  type="link"
                  disabled={isDemoRef.current}
                  onClick={() => handleClickEdit(item)}
                >
                  {intlTrans('commodity.设置')}
                </Button>
              </div>
            </div>
          )
        },
        renderFormItem: (schema: any, config: any, form: any, action: any) => {
          return (
            <Select
              {...config.fieldProps}
              style={{ width: '150px' }}
              options={supplierShopsRef.current}
              dropdownStyle={{ zIndex: 99 }}
              allowClear
            />
          )
        },
        formItemProps: {
          label: intlTrans('sample.店铺'),
          name: 'shopCode'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseSelect')
        }
      }
    ]
  }, [supplierShopsRef.current])

  const handleEditModal = (value: any) => {
    formRef.current?.submit()
    setEditModalOpen(false)
  }

  return (
    <>
      {isDemo ? (
        <Alert
          message={
            <div className={styles.tips_box}>
              <div>{intlTrans('analytics.当前展示商品')}</div>
              <Button
                disabled={authDisabled}
                type="link"
                onClick={() => {
                  handleToTiktokShop()
                }}
              >
                {intlTrans('analytics.去授权TikTok店铺')}
              </Button>
            </div>
          }
          type="info"
          showIcon
        />
      ) : null}
      <Table
        className="commodity-warehouse-table"
        columns={columns}
        bordered={true}
        request={onRequest}
        actionRef={actionRef}
        toolBarRender={false}
        formRef={formRef}
        rowKey={(_, index) => index as number}
        searchExtra={[
          <div
            key="extra-buttons"
            style={{ display: 'flex', gap: '8px', marginLeft: '-124px', marginTop: '40px' }}
          >
            <Button
              type="default"
              disabled={isDemo}
              onClick={() => {
                formRef.current?.resetFields()
                formRef.current?.submit()
              }}
            >
              {intlTrans('common.Reset')}
            </Button>
            <Button
              type="primary"
              disabled={isDemo}
              onClick={() => {
                formRef.current?.submit()
              }}
            >
              {intlTrans('common.Search')}
            </Button>
            {/* <Export key="export" downloadRequest={downloadRequest} childrenType={21} /> */}
          </div>
        ]}
      />
      {loading && <Spin spinning className="spin-loading" />}
      <EditModal
        isEditModalOpen={isEditModalOpen}
        handleOk={(values) => {
          handleEditModal(values)
        }}
        handleCancel={() => {
          setEditModalOpen(false)
        }}
        item={editValue}
      />
    </>
  )
}

export default withPageTitle(ManageContent, intlTrans('commodity.商品管理'))
