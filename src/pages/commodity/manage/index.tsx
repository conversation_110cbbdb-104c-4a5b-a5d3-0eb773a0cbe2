import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components'
import { useRef, useState, useEffect } from 'react'
import 'moment-timezone'
import { useQuery } from '@/hooks'
import LockDemo from '@/components/LockDemo'
import ManageContent from './components/manageContent'
import { useHistory } from 'umi'
import { suppliersInfo } from '@/api/analytics'
import Loading from '@/loading'
import { isAuthShopStorage } from '@/utils/storage'
import { message } from 'antd'
import { intlTrans } from '@/utils/common'
import useSampleReview from '@/hooks/useSampleReview'

const Commodity = () => {
  const queryParams = useQuery()
  const [isShowDemo, setIsShowDemo] = useState<any>(undefined)
  const history = useHistory()
  useSampleReview()
  function closeParentPage() {
    if (window.opener) {
      window.opener.close()
    }
  }
  const isHaveAuthShop = () => {
    suppliersInfo().then((res) => {
      if (res.code == 200) {
        const { isAuthShop } = res.result
        setIsShowDemo(!isAuthShop)
        if (isAuthShop) {
          isAuthShopStorage.set('true')
        } else {
          isAuthShopStorage.set('false')
        }
      } else {
        setIsShowDemo(true)
      }
    })
  }
  useEffect(() => {
    if (
      !queryParams ||
      (queryParams && !queryParams.isDemo) ||
      (queryParams && queryParams.isDemo && queryParams.status == '1')
    ) {
      isHaveAuthShop()
    }
  }, [])
  useEffect(() => {
    const { status, errorMsg, isDemo } = queryParams
    let url = '/commodity/manage'
    if (isDemo) {
      setIsShowDemo(false)
      url = '/commodity/manage?isDemo=true'
    }
    if (status === '0' && errorMsg) {
      message.error(errorMsg)
      history.push(url)
      closeParentPage()
    }
    if (status === '1') {
      message.success(intlTrans('locale.授权成功'))
      history.push(url)
      closeParentPage()
    }
  }, [])

  return (
    <>
      {isShowDemo != undefined ? (
        !isShowDemo ? (
          <ManageContent />
        ) : (
          <LockDemo
            type={1}
            onShowDemo={() => {
              setIsShowDemo(false)
              history.push('/commodity/manage?isDemo=true')
            }}
          />
        )
      ) : (
        Loading()
      )}
    </>
  )
}

export default Commodity
