import styles from './index.less'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { intlTrans, replaceExceptNumber } from '@/utils/common'
import { Button, Form, Input, message, Modal, Select } from 'antd'
import NumberInput from '@/components/NumberInput'
import { updateCost } from '@/api/analytics'

interface Props {
  isEditModalOpen: boolean
  handleOk: (e: any) => void
  handleCancel: () => void
  item: any
}

export default function EditModal({ isEditModalOpen, handleOk, handleCancel, item }: Props) {
  const [form] = Form.useForm()
  const [confirmBtnViable, setConfirmBtnViable] = useState(false)
  const materialType = Form.useWatch('materialType', form)
  const handleClickOk = () => {
    form
      .validateFields()
      .then((values) => {
        setConfirmBtnViable(true)
        values.videoId = item?.videoId
        values.shopCode = item?.shopCode

        updateCost(values).then((res) => {
          if (res.code === 200 && res.result) {
            message.success(intlTrans('common.OperationSuccessful'))
            handleOk(values)
            form.resetFields()
          } else {
            handleOk(values)
            message.error(intlTrans('common.OperationFailed'))
          }
        })
      })
      .catch((errorInfo) => {
        console.log(errorInfo)
      })
      .finally(() => {
        setConfirmBtnViable(false)
      })
  }

  useEffect(() => {
    if (item) {
      form.setFieldValue('materialType', item.materialType ? item?.materialType.toString() : '2')
      if (item.materialType == '1') {
        form.setFieldValue('fee', item?.positionCost)
      }
    }
  }, [item, form])
  return (
    <Modal
      title={
        <div className={styles.modal_box}>
          {intlTrans('analytics.坑位费')}
          <div className={styles.modal_title_tips}>{intlTrans('analytics.如为纯佣素材')}</div>
        </div>
      }
      cancelText={intlTrans('common.Cancel')}
      okText={intlTrans('common.Sure')}
      open={isEditModalOpen}
      onOk={handleClickOk}
      onCancel={
        () => {
          handleCancel()
          if (item) {
            form.setFieldValue(
              'materialType',
              item.materialType ? item?.materialType.toString() : '2'
            )
            if (item.materialType == '1') {
              form.setFieldValue('fee', item?.positionCost)
            }
          }
        }
        // form.resetFields()
      }
      confirmLoading={confirmBtnViable}
    >
      <Form
        form={form}
        name="form"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 300 }}
        initialValues={{ materialType: '2' }}
        onFinish={(values) => {
          handleClickOk()
        }}
      >
        <Form.Item
          name="materialType"
          label={intlTrans('analytics.素材类型')}
          rules={[{ required: true }]}
        >
          <Select
            options={[
              { label: intlTrans('analytics.纯佣'), value: '2' },
              { label: intlTrans('analytics.纯佣坑位费'), value: '1' }
            ]}
          />
        </Form.Item>

        {materialType == '1' ? (
          <Form.Item name="fee" label={intlTrans('analytics.坑位费')} rules={[{ required: true }]}>
            <NumberInput autoComplete="off" isWithPoint />
          </Form.Item>
        ) : null}

        <Form.Item>
          <Button type="primary" htmlType="submit" />
        </Form.Item>
      </Form>
    </Modal>
  )
}
