import withPageTitle from '@/HOCs/withPageTitle'
import ProductImage from '@/components/ProductImage'
import Table from '@/components/Table'
import useTableRequest from '@/hooks/useTableRequest'
import { intlTrans } from '@/utils/common'
import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components'
import { useBoolean, useRequest } from 'ahooks'
import type { TablePaginationConfig } from 'antd'
import {
  Button,
  Spin,
  message,
  Image,
  Tag,
  Select,
  Input,
  DatePicker,
  Tooltip,
  Alert,
  Form
} from 'antd'
import { useEffect, useMemo, useRef, useState } from 'react'
import { Export } from '@/components/Export'
import type { GetVideoPerformanceParams } from '@/api/analytics'
import {
  haveShopSubAccount,
  validShops,
  videoPerformanceExport,
  videoPerformanceList,
  videoPerformanceMock,
  videoPerformanceOverview,
  videoPerformanceOverviewMock
} from '@/api/analytics'
import styles from './index.less'
import {
  convertToThailandTimestamp,
  formatPrice,
  formatSales,
  formatThTime,
  formatUnit
} from '@/utils/format'
import AvatarImage from '@/components/AvatarImage'
import moment from 'moment'
import 'moment-timezone'
import ErrorImage from '@/components/ErrorImage'
import type { FilterValue } from 'antd/lib/table/interface'
import Copy from '@/components/Copy'
import EditModal from '../EditModal'
import SampleCostModal from '../sampleCostModal'
import DataCard from '../dataCard'
import { useQuery } from '@/hooks'
import { useHistory } from 'umi'
import shopApi from '@/api/shop'
import CustomizeAvatar from '@/components/Avatar'
import defalut_video from '@/assets/images/analytics/defalut_video.png'
import { copy } from '@/utils/copy'
const VideoPerContent = () => {
  const [isEditModalOpen, setEditModalOpen] = useState(false)
  const [sampleCostModalVisible, setSampleCostModalVisible] = useState(false)
  const [loading, { toggle: loadingToggle }] = useBoolean()
  const [editValue, setEditValue] = useState<any>({})
  const actionRef = useRef<ActionType>()
  const formRef = useRef<FormInstance>()
  const [requestedParams, setRequestedParams] = useState<GetVideoPerformanceParams>()
  const timezone = 'Asia/Bangkok'
  const queryParams = useQuery()
  const isDemoRef = useRef(false)
  const [isDemo, setIsDemo] = useState(false)
  const [authDisabled, setAuthDisabled] = useState(false)
  const [supplierShops, setSupplierShops] = useState([])
  const [overviewData, setOverviewData] = useState({})
  const totalRowRef = useRef(-1)
  const [sorterState, setSorterState] = useState<any>({})
  const [haveSubAccount, setHaveSubAccount] = useState<boolean>(false)
  const options = [
    // { label: intlTrans('common.All'), value: '0' },
    { label: intlTrans('analytics.商品id'), value: '1' },
    { label: intlTrans('analytics.视频标题'), value: '2' },
    { label: intlTrans('analytics.视频Id'), value: '3' },
    { label: intlTrans('analytics.达人名称'), value: '4' }
  ]
  function closeParentPage() {
    if (window.opener) {
      window.opener.close() // 关闭原页面
    }
  }
  const handleHaveAccount = async () => {
    const res = await haveShopSubAccount()
    if (res.code == 200) {
      setHaveSubAccount(res.result)
    }
  }
  useEffect(() => {
    handleHaveAccount()
    const { status, errorMsg } = queryParams
    if (status === '0' && errorMsg) {
      message.error(errorMsg)
      window.history.replaceState(null, '', window.location.pathname)
      closeParentPage()
    }
    if (status === '1') {
      message.success(intlTrans('locale.授权成功'))
      window.history.replaceState(null, '', window.location.pathname)
      closeParentPage()
    }
  }, [])

  const handleOverviewMock = async (params: any) => {
    const res = isDemo
      ? await videoPerformanceOverviewMock()
      : await videoPerformanceOverview(params)
    if (res.code == 200) {
      setOverviewData(res.result)
    }
  }
  const handleShop = async () => {
    const res = await validShops()
    if (res.code === 200) {
      const value = res.result.map((item: any) => {
        return {
          label: item.shopName,
          value: item.shopCode
        }
      })
      setSupplierShops(value)
    }
  }

  useEffect(() => {
    handleShop()
    if (queryParams && queryParams.isDemo) {
      setIsDemo(queryParams.isDemo)
      isDemoRef.current = queryParams.isDemo
    }
  }, [])

  const handleToTiktokShop = async () => {
    try {
      setAuthDisabled(true)
      const result = await shopApi.getTiktokAuthUrl('/dataAnalytics/videoPerformance?isDemo=true')
      setAuthDisabled(false)
      if (result.code == 200 && result.result) {
        window.open(result.result, '_blank')
      }
    } catch (error) {
      setAuthDisabled(false)
    }
  }
  const onRequest = useTableRequest(async (params) => {
    try {
      params.totalRow = totalRowRef.current
      if (params.statisticsTime) {
        if (params.statisticsTime[0]) {
          params.startTime = convertToThailandTimestamp(params.statisticsTime[0], false)
        }
        if (params.statisticsTime[1]) {
          params.endTime = convertToThailandTimestamp(params.statisticsTime[1], true)
        }
      } else {
        params.endTime = ''
        params.startTime = ''
      }
      delete params.statisticsTime
      if (params.videoPublishTime) {
        if (params.videoPublishTime[0]) {
          if (params.videoPublishTime[0]) {
            const value = moment(params.videoPublishTime[0]).startOf('day').valueOf()
            params.videoPublishStartTime = moment(value).add(1, 'hour').valueOf() // 设置开始时间 +1小时
          }
        }
        if (params.videoPublishTime[1]) {
          const value = moment(params.videoPublishTime[1]).endOf('month').valueOf()
          params.videoPublishEndTime = moment(value).add(1, 'hour').valueOf()
        }
      } else {
        formRef.current?.setFieldValue('videoPublishTime', [
          moment().tz(timezone).startOf('month'),
          moment().tz(timezone).endOf('month')
        ])
        params.videoPublishStartTime = convertToThailandTimestamp(null, false, 'month')
        params.videoPublishEndTime = convertToThailandTimestamp(null, true, 'month')
      }
      delete params.videoPublishTime
      if (!params.searchType) {
        params.searchType = '1'
      }
      if (!params.param) {
        params.searchType = ' '
      }
      if (!params.investmentFlowStatus && haveSubAccount) {
        params.investmentFlowStatus = 99
      }

      if (!params.materialType) {
        params.materialType = 99
      }
      setRequestedParams(params)
      handleOverviewMock(params)
      const res = isDemo ? await videoPerformanceMock(params) : await videoPerformanceList(params)
      if (res.code == 200) {
        totalRowRef.current = res.result.total
      }
      return Promise.resolve(res)
    } catch (error) {
      return Promise.reject(error)
    }
  })

  const surrenderTag = (type: number) => {
    switch (type) {
      case 2:
        return <Tag color="success">{intlTrans('analytics.未投流')}</Tag>
      case 1:
        return <Tag color="red">{intlTrans('analytics.已投流')}</Tag>
      case 3:
        return <Tag color="default">{intlTrans('analytics.已关闭')}</Tag>
      default:
        return <Tag color="success">{intlTrans('analytics.未投流')}</Tag>
    }
  }
  //点击编辑按钮触发
  const handleClickEdit = (item: any) => {
    setEditModalOpen(true)
    setEditValue(item)
  }
  //点击样品成本的弹窗
  const handleClickSampleCostEdit = (item: any) => {
    setEditValue(item)
    setSampleCostModalVisible(true)
  }
  const columns = useMemo<ProColumns<any>[]>(() => {
    return [
      //视频信息
      {
        title: intlTrans('analytics.视频信息'),
        dataIndex: 'title',
        fixed: true,
        align: 'center',
        width: 250,
        order: 95,
        hideInSearch: haveSubAccount ? false : true,
        renderText(text, item) {
          return (
            <div className={styles.video_box}>
              <div
                onClick={() => {
                  window.open(`https://www.tiktok.com/player/v1/${item.videoId}?id=${item.videoId}`)
                }}
                className={styles.product_img}
              >
                <div
                  onClick={() => {
                    window.open(item.videoUrl)
                  }}
                >
                  <Image src={defalut_video} className={styles.product_img} preview={false} />
                </div>
                <div>
                  {/* {item.cover ? (
                    <ProductImage src={item.cover} isNoPreview={true} />
                  ) : (
                    <ErrorImage />
                  )} */}
                </div>
              </div>
              <div className={styles.video_content}>
                <div className={styles.title_box}>
                  {haveSubAccount && <div>{surrenderTag(item.investmentFlowStatus)}</div>}
                  <Tooltip title={item.title}>
                    <span className={styles.video_title}>{item.title}</span>
                  </Tooltip>
                </div>
                {item.adsCode && (
                  <div
                    onClick={() => {
                      copy(item.adsCode)
                    }}
                  >
                    <Copy text={item.adsCode} />

                    <span style={{ color: '#009995', cursor: 'pointer' }}>
                      {intlTrans('analytics.获取投流码')}
                    </span>
                  </div>
                )}
                <div className={styles.video_time}>
                  {intlTrans('analytics.发布时间')}: {formatThTime(item.publishTime)}{' '}
                </div>
                {haveSubAccount && (
                  <div className={styles.video_duration}>视频时长:{item.duration || 0}s </div>
                )}
              </div>
            </div>
          )
        },
        // 当前投流状态
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <Select
              {...config.fieldProps}
              defaultValue="99"
              dropdownStyle={{ zIndex: 99 }}
              allowClear
            />
          )
        },
        formItemProps: {
          label: intlTrans('analytics.当前投流状态'),
          name: 'investmentFlowStatus'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseSelect'),
          options: [
            { label: intlTrans('common.All'), value: '99' },
            { label: intlTrans('analytics.已投流'), value: '1' },
            { label: intlTrans('analytics.未投流'), value: '2' },
            { label: intlTrans('analytics.已停投'), value: '3' }
          ],
          onChange: () => {
            totalRowRef.current = -1
          }
        }
      },
      // 商品信息
      {
        title: intlTrans('analytics.商品信息'),
        dataIndex: 'productCover',
        align: 'center',
        fixed: true,
        order: 95,
        colSize: 1.2,
        width: 160,
        renderText(text, product) {
          return (
            <>
              {/* <div>{JSON.stringify(product.items)}</div> */}
              <div className={styles.product_box}>
                {(product?.items || []).length > 0 ? (
                  product.items.map((item: any, index: any) => {
                    return (
                      <div key={index} className={styles.product_img_box}>
                        {item.productTitle && item.minPrice && item.productId ? (
                          <Tooltip
                            title={
                              <div className={styles.video_content}>
                                <div className={styles.title_box}>
                                  <span className={styles.video_title}>
                                    {item.productTitle || '-'}
                                  </span>
                                </div>

                                <div className={styles.video_id}>
                                  {intlTrans('analytics.ID')}:{item.productId || '-'}
                                </div>
                              </div>
                            }
                          >
                            <div>
                              {item.productCover ? (
                                <ProductImage src={item.productCover} width={60} />
                              ) : (
                                <ErrorImage />
                              )}
                            </div>
                          </Tooltip>
                        ) : (
                          '-'
                        )}
                      </div>
                    )
                  })
                ) : (
                  <div className={styles.no_data}>-</div>
                )}
              </div>
            </>
          )
        },
        //素材类型
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <Select
              style={{ width: '300px' }}
              {...config.fieldProps}
              defaultValue={'99'}
              allowClear
            />
          )
        },
        formItemProps: {
          label: intlTrans('analytics.素材类型'),
          name: 'materialType'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseEnter'),
          options: [
            { label: intlTrans('common.All'), value: '99' },
            { label: intlTrans('analytics.付费商单'), value: '4' },
            { label: intlTrans('analytics.YoupikTAP'), value: '1' },
            { label: intlTrans('analytics.店铺纯佣(公开/定向)'), value: '2' },
            { label: intlTrans('analytics.特殊渠道（仅稚优泉特定tap申样标签）'), value: '3' },
            { label: intlTrans('analytics.未分类'), value: '5' }
          ],
          onChange: () => {
            totalRowRef.current = -1
            console.log('111素材类型')
          }
        }
      },
      // 达人信息
      {
        title: intlTrans('analytics.达人信息'),
        dataIndex: 'avatar',
        align: 'center',
        fixed: true,
        order: 96,
        width: 200,
        hideInSearch: true,
        renderText(text, item) {
          return (
            <div className={styles.master_box}>
              <div
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  window.open(item.homePageUrl)
                }}
              >
                {<CustomizeAvatar avatar={text} />}
              </div>
              <div className={styles.master_content}>
                <Tooltip title={item.nickName}>
                  <div
                    className={styles.video_title}
                    onClick={() => {
                      window.open(item.homePageUrl)
                    }}
                  >
                    {item.nickname}
                  </div>
                </Tooltip>
                <div className={styles.master_uniqueName}>@{item.displayName}</div>
              </div>
            </div>
          )
        }
      },
      // 效果评估formatPrice
      {
        title: intlTrans('analytics.效果评估'),
        order: 100,
        align: 'center',
        hideInTable: haveSubAccount ? false : true,
        initialValue: [
          moment().tz(timezone).startOf('month'),
          moment().tz(timezone).endOf('month')
        ],
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <DatePicker.RangePicker
              {...config.fieldProps}
              placeholder={[intlTrans('analytics.起始日期'), intlTrans('analytics.结束日期')]}
              picker="month"
              popupClassName={styles.custom_month_picker}
              style={{ width: '200px' }}
            />
          )
        },
        formItemProps: {
          label: intlTrans('analytics.视频发布月份'),
          name: 'videoPublishTime'
        },
        fieldProps: {
          onChange: () => {
            totalRowRef.current = -1
          }
        },

        children: [
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选统计周期')}>
                {intlTrans('analytics.综合ROI')}
              </Tooltip>
            ),
            dataIndex: 'roi',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.roi || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 100,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatPrice(text) : '-'}</div>
            }
          }
        ]
      },
      //成交信息
      {
        title: intlTrans('analytics.成交信息'),
        order: 94,
        colSize: 1,
        align: 'center',
        initialValue: [
          moment().tz(timezone).subtract(7, 'days'),
          moment().tz(timezone).subtract(1, 'days')
        ],
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <DatePicker.RangePicker
              {...config.fieldProps}
              placeholder={[intlTrans('common.StartDate'), intlTrans('common.EndDate')]}
              style={{ width: '300px' }}
            />
          )
        },
        formItemProps: {
          label: intlTrans('analytics.统计周期'),
          name: 'statisticsTime'
        },
        fieldProps: {
          onChange: () => {
            totalRowRef.current = -1
          }
        },

        children: [
          {
            title: (
              <Tooltip title="筛选统计周期内本条内容创造的GMV（非自然+自然）">
                {intlTrans('analytics.综合GMV')}
              </Tooltip>
            ),
            dataIndex: 'gmv',
            sorter: true,
            sortOrder: sorterState.gmv || null,
            sortDirections: ['descend', 'ascend'],
            align: 'center',
            showSorterTooltip: false,
            hideInSearch: true,
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatPrice(text, true) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.本条内容发布7天创造的GMV')}>
                {intlTrans('analytics.发布后7天GMV')}
              </Tooltip>
            ),
            dataIndex: 'gmv7d',
            align: 'center',
            order: 100,
            sorter: true,
            sortOrder: sorterState.gmv7d || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatPrice(text, true) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.本条内容发布14天创造的GMV')}>
                {intlTrans('analytics.发布后14天GMV')}
              </Tooltip>
            ),
            dataIndex: 'gmv14d',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.gmv14d || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatPrice(text, true) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.本条内容发布28天创造的GMV')}>
                {intlTrans('analytics.发布后28天GMV')}
              </Tooltip>
            ),
            dataIndex: 'gmv28d',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.gmv28d || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatPrice(text, true) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选统计周期内本条内容每个SKU')}>
                {intlTrans('analytics.综合SKU订单')}
              </Tooltip>
            ),
            dataIndex: 'orderNum',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.orderNum || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatSales(text) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选统计周期内本条内容创造的销售件数')}>
                {intlTrans('analytics.综合销量')}
              </Tooltip>
            ),
            dataIndex: 'sales',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.sales || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatSales(text) : '-'}</div>
            }
          }
        ]
      },
      //成本信息
      {
        title: intlTrans('analytics.成本信息'),
        order: 97,
        colSize: 1.5,
        align: 'center',
        renderFormItem: (schema: any, config: any, form: any, action: any) => {
          return (
            <Input.Group compact style={{ display: 'flex', marginLeft: '12px' }}>
              <Form.Item name="searchType" noStyle>
                <Select
                  {...config.fieldProps}
                  options={options}
                  style={{ width: 120 }}
                  dropdownStyle={{ zIndex: 99 }}
                  label={intlTrans('analytics.搜索类型')}
                  placeholder={intlTrans('common.PleaseSelect')}
                  defaultValue="1"
                  onChange={() => {
                    totalRowRef.current = -1
                  }}
                />
              </Form.Item>
              <Form.Item name="param" noStyle>
                <Input
                  {...config.formItemProps}
                  style={{ width: '400px' }}
                  allowClear
                  placeholder={intlTrans('analytics.按商品ID')}
                  onChange={() => {
                    totalRowRef.current = -1
                  }}
                />
              </Form.Item>
            </Input.Group>
          )
        },
        formItemProps: {
          label: ''
        },
        fieldProps: {
          label: intlTrans('analytics.搜索类型'),
          name: 'searchType',
          placeholder: intlTrans('common.PleaseEnter')
        },
        children: [
          haveSubAccount && {
            title: (
              <Tooltip title={intlTrans('analytics.筛选统计周期内通过GMVMax投流花费的总成本')}>
                {intlTrans('analytics.投流成本')}
              </Tooltip>
            ),
            dataIndex: 'investmentCost',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.investmentCost || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return (
                <div>
                  {item.investmentCost || item.investmentCost === 0
                    ? formatPrice(item.investmentCost, true)
                    : '-'}
                </div>
              )
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.本条内容除了销售佣金以外的单次发布费用')}>
                {intlTrans('analytics.坑位费')}
              </Tooltip>
            ),
            dataIndex: 'positionCost',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.position_cost || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return (
                <div className={styles.item_box}>
                  {item.materialType == 2 || item.materialType === 0 ? (
                    <Tag color="default">{intlTrans('analytics.纯佣')}</Tag>
                  ) : (
                    <div className={styles.item_word}>
                      {item.positionCost || item.positionCost === 0 ? (
                        formatPrice(item.positionCost, true)
                      ) : (
                        <Tag color="default">{intlTrans('analytics.纯佣')}</Tag>
                      )}
                    </div>
                  )}

                  <Button
                    type="link"
                    className={styles.edit_btn}
                    disabled={isDemoRef.current}
                    onClick={() => {
                      handleClickEdit(item)
                    }}
                  >
                    {intlTrans('common.Edit')}
                  </Button>
                </div>
              )
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.本条内容产生的样品成本')}>
                {intlTrans('analytics.样品成本')}
              </Tooltip>
            ),
            dataIndex: 'sample_cost',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.sample_cost || null,
            width: '250px',
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            renderText(text, item) {
              return (
                <div className={styles.item_box}>
                  <div className={styles.item_word}>
                    {item.sampleCost || item.sampleCost == 0
                      ? formatPrice(item.sampleCost, true)
                      : '/'}
                  </div>

                  <Button
                    type="link"
                    className={
                      (item?.items || []).length == 0 ? styles.disabled_btn : styles.edit_btn
                    }
                    disabled={(item?.items || []).length == 0 || isDemoRef.current}
                    onClick={() => {
                      handleClickSampleCostEdit(item)
                    }}
                  >
                    {intlTrans('common.Edit')}
                  </Button>
                </div>
              )
            }
          },
          //佣金成本
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选统计周期内本条内容需要支付')}>
                {intlTrans('analytics.佣金成本')}
              </Tooltip>
            ),

            dataIndex: 'commissionCost',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.commissionCost || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return (
                <div className={styles.item_word}>
                  {text || text === 0 ? formatPrice(text, true) : '-'}
                </div>
              )
            }
          }
        ].filter(Boolean)
      },
      //流量信息
      {
        title: intlTrans('analytics.流量信息'),
        align: 'center',
        colSize: 0.5,
        //投流码
        renderFormItem: (schema, config: any, form, action) => {
          return (
            <Select
              {...config.fieldProps}
              dropdownStyle={{ zIndex: 99, width: '150px', marginRight: '20px' }}
              allowClear
            />
          )
        },
        formItemProps: {
          label: intlTrans('analytics.投流码'),
          name: 'hasAdsCode'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseSelect'),
          options: [
            { label: intlTrans('analytics.有投流码'), value: true },
            { label: intlTrans('analytics.无投流码'), value: false }
          ],
          onChange: () => {
            totalRowRef.current = -1
          }
        },
        children: [
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选周期范围内本条内容的曝光量')}>
                {intlTrans('analytics.视频流量总量')}
              </Tooltip>
            ),
            dataIndex: 'videoFlow',
            align: 'center',
            sorter: true,
            sortOrder: sorterState.videoFlow || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return (
                <div>
                  {item.videoFlow || item.videoFlow === 0
                    ? formatUnit(item.videoFlow, '', true)
                    : '-'}
                </div>
              )
            }
          },
          haveSubAccount && {
            title: (
              <Tooltip title={intlTrans('analytics.筛选周期范围内本条内容的曝光量')}>
                {intlTrans('analytics.商品曝光量')}
              </Tooltip>
            ),
            dataIndex: 'exposure',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.exposure || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text === 0 ? formatSales(text) : '-'}</div>
            }
          },
          {
            title: (
              <Tooltip title={intlTrans('analytics.筛选周期范围内本条内容的商品点击量')}>
                {intlTrans('analytics.商品点击量')}
              </Tooltip>
            ),
            dataIndex: 'click',
            align: 'center',
            hideInSearch: true,
            sorter: true,
            sortOrder: sorterState.click || null,
            showSorterTooltip: false,
            sortDirections: ['descend', 'ascend'],
            width: 120,
            renderText(text, item) {
              return <div>{text || text == 0 ? formatUnit(text, '0') : '-'}</div>
            }
          }
        ].filter(Boolean)
      },
      //额外的搜索项
      {
        dataIndex: 'cover',
        hideInTable: true,
        order: 96,
        align: 'center',
        renderFormItem: (schema: any, config: any, form: any, action: any) => {
          return <Select {...config.fieldProps} dropdownStyle={{ zIndex: 99 }} allowClear />
        },
        formItemProps: {
          label: intlTrans('sample.店铺'),
          name: 'shopCode'
        },
        fieldProps: {
          placeholder: intlTrans('common.PleaseSelect'),
          options: supplierShops,
          onChange: () => {
            totalRowRef.current = -1
          }
        }
      }
    ]
  }, [supplierShops, sorterState, haveSubAccount])

  const downloadRequest = () => videoPerformanceExport(requestedParams || {})

  const handleEditModal = (value: any) => {
    setEditModalOpen(false)
    setSampleCostModalVisible(false)
    formRef.current?.submit()
  }
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: any,
    extra: { currentDataSource: any; action: 'paginate' | 'sort' | 'filter' }
  ) => {
    if (extra.action === 'paginate') {
      setSorterState(sorterState)
      if (actionRef?.current?.pageInfo) {
        actionRef.current.pageInfo.current = pagination.current || 1
      }
    } else if (extra.action === 'sort') {
      const newSorter: any = {}
      if (sorter.field) {
        newSorter[sorter.field] = sorter.order
      }
      setSorterState(newSorter)
      if (Object.keys(sorter).length !== 0) {
        if (actionRef.current?.pageInfo) {
          actionRef.current.pageInfo.current = 1
        }
      }
    }
  }

  return (
    <>
      {isDemo ? (
        <Alert
          message={
            <div className={styles.tips_box}>
              <div>{intlTrans('analytics.当前展示数据罗盘')}</div>
              <Button
                disabled={authDisabled}
                type="link"
                onClick={() => {
                  handleToTiktokShop()
                }}
              >
                {intlTrans('analytics.去授权TikTok店铺')}
              </Button>
            </div>
          }
          type="info"
          showIcon
        />
      ) : null}
      <Table
        className="commodity-warehouse-table"
        columns={columns}
        bordered={true}
        request={onRequest}
        actionRef={actionRef}
        toolBarRender={false}
        formRef={formRef}
        scroll={{ y: 1500, x: 3100 }}
        rowKey={(_, index) => index as number}
        onChange={handleTableChange}
        options={{
          reload: false,
          setting: false,
          density: false
        }}
        tableRender={(tableProps, defaultTable) => {
          return (
            <>
              <div style={{ backgroundColor: '#fff' }}>
                <DataCard data={overviewData} haveSubAccount={haveSubAccount} />
              </div>
              {defaultTable}
            </>
          )
        }}
        searchExtra={[
          <div key="extra-buttons" className={styles.search_btns} style={{ marginLeft: '150px' }}>
            <Button
              type="default"
              disabled={isDemo}
              onClick={() => {
                setSorterState({})
                totalRowRef.current = -1
                actionRef.current?.reset()
                actionRef.current?.reload()
                formRef.current?.resetFields()
                formRef.current?.submit()
              }}
            >
              {intlTrans('common.Reset')}
            </Button>
            <Button
              type="primary"
              disabled={isDemo}
              onClick={() => {
                formRef.current?.submit()
              }}
            >
              {intlTrans('common.Search')}
            </Button>
            <Export
              key="export"
              downloadRequest={downloadRequest}
              childrenType={21}
              isDemo={isDemo}
            />
          </div>
        ]}
        searchSpan={{
          xs: 12,
          sm: 12,
          md: 12,
          lg: 12,
          xl: 12,
          xxl: 8
        }}
      />
      {loading && <Spin spinning className="spin-loading" />}
      <EditModal
        isEditModalOpen={isEditModalOpen}
        handleOk={(values) => {
          handleEditModal(values)
        }}
        handleCancel={() => {
          setEditModalOpen(false)
        }}
        item={editValue}
      />
      <SampleCostModal
        isEditModalOpen={sampleCostModalVisible}
        handleOk={(values) => {
          handleEditModal(values)
        }}
        handleCancel={() => {
          setSampleCostModalVisible(false)
        }}
        item={editValue}
      />
    </>
  )
}

export default withPageTitle(VideoPerContent, intlTrans('analytics.视频表现'))
