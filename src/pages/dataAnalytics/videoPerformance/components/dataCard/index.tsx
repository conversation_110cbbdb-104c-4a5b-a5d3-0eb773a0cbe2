import { intlTrans } from '@/utils/common'
import { formatPrice, formatSales, formatUnit } from '@/utils/format'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Button, Card, Tooltip } from 'antd'
import React, { useState, useEffect } from 'react'
import styles from './index.less'

const DataCard = (props: any) => {
  // 设置初始的 data 状态
  const [data, setData] = useState(props.data)
  const [showData, setShowData] = useState(false)

  // 监听 data 变化
  useEffect(() => {
    if (props.data !== data) {
      setData(props.data) // 当 props.data 变化时更新状态
    }
  }, [props.data]) // 依赖项是 props.data，当它变化时会触发

  const arr = [
    {
      title: intlTrans('analytics.GMV'),
      value: formatPrice(data.gmv || 0, true),
      tip: intlTrans('analytics.gmvTips')
    },
    {
      title: intlTrans('analytics.销量'),
      value: formatSales(data.sales || 0),
      tip: intlTrans('analytics.销量Tips')
    },
    {
      title: intlTrans('analytics.订单'),
      value: formatSales(data.orderCount || 0),
      tip: intlTrans('analytics.订单Tips')
    },
    {
      title: intlTrans('analytics.内容总曝光量'),
      value: formatUnit(data.videoExposureCount || 0, '', true),
      tip: intlTrans('analytics.内容总曝光量Tips')
    },
    {
      title: intlTrans('analytics.商品总点击量'),
      value: formatUnit(data.productClickCount || 0, '', true),
      tip: intlTrans('analytics.商品总点击量Tips')
    },

    ...(showData
      ? [
          props?.haveSubAccount && {
            title: intlTrans('analytics.商品总曝光量'),
            value: formatUnit(data.productExposureCount),
            tip: intlTrans('analytics.商品总曝光量Tips')
          },
          {
            title: intlTrans('analytics.总支出'),
            value: formatPrice(data.payTotal || 0, true),
            tip: intlTrans('analytics.总支出Tips')
          },
          {
            title: intlTrans('analytics.总坑位费'),
            value: formatPrice(data.positionCostTotal || 0, true),
            tip: intlTrans('analytics.总坑位费Tips')
          },
          props?.haveSubAccount && {
            title: intlTrans('analytics.总投流成本'),
            value: formatPrice(data.investmentFlowTotal, true),
            tip: intlTrans('analytics.总投流成本Tips')
          },
          {
            title: intlTrans('analytics.总联盟佣金'),
            value: formatPrice(data.commissionCostTotal || 0, true),
            tip: intlTrans('analytics.总联盟佣金Tips')
          }
        ]
      : [])
  ]

  return (
    <div className={styles.box}>
      {arr.map((item, index) => (
        <Card
          key={index}
          bordered={true}
          style={{ marginBottom: '20px', marginRight: index == arr.length - 1 ? 0 : '20px' }}
          className={styles.card_boxs}
        >
          <div>
            {item.title}
            <Tooltip title={item.tip} className={styles.tooltip}>
              <QuestionCircleOutlined />
            </Tooltip>{' '}
          </div>
          <p className={styles.value}>{item.value}</p>
        </Card>
      ))}
      <Button type="link" onClick={() => setShowData(!showData)}>
        {!showData ? intlTrans('analytics.展开面板') : intlTrans('analytics.收起面板')}
      </Button>
    </div>
  )
}

export default DataCard
