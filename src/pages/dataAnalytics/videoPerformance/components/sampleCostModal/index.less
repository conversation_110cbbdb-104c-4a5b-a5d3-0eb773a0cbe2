.modal_box {
  position: relative;
  display: flex;
}
.modal_title_tips {
  margin-left: 24px;
  color: #ccc;
  font-size: 16px;
}
.single_item {
  display: flex;
}
.single_item_img {
  width: 100px;
  height: 100px;
  margin-right: 10px;
}
.product_box {
  display: flex;
}
.product_title {
  width: 300px;
}
.product_label {
  width: 80px;
}
.input_box {
  margin-top: 30px;
}

.single_sample_cost_box {
  display: flex;
}
.sample_cost_input {
  margin-left: 20px;
}
.total_box {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.sample_cost_total {
  color: #fe2c55;
}
.sample_cost_total_box {
  font-size: 20px;
}

.spin_loading {
  position: absolute !important;
  top: 100px;
  left: 50%;
  z-index: 200;
}

.form_box {
  border: 1px solid #ccc;
}
