import styles from './index.less'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { intlTrans, replaceExceptNumber } from '@/utils/common'
import type { FormInstance } from 'antd'
import { Button, Form, Input, InputNumber, message, Modal, Select, Spin, Tabs } from 'antd'
import NumberInput from '@/components/NumberInput'
import { formatMoney } from '@/utils/format'
import { editSampleNum, getSampleCost, updateCost } from '@/api/analytics'
interface Props {
  isEditModalOpen: boolean
  handleOk: (e: any) => void
  handleCancel: () => void

  item: any
}

export default function SampleCostModal({ isEditModalOpen, handleOk, handleCancel, item }: Props) {
  const [confirmBtnViable, setConfirmBtnViable] = useState(false)
  const [sampleCosts, setSampleCosts] = useState<number[]>([])
  const [productItem, setProductItem] = useState<any>()
  const formRefs = useRef<(FormInstance<any> | null)[]>([])
  const [requestViable, setRequestViable] = useState(false)
  const [sampleNumTotal, setSampleNumTotal] = useState(0)
  const [sampleCostTotal, setSampleCostTotal] = useState(0)

  const setFormValues = (value: any) => {
    if (!formRefs.current || !value?.videoSampleInfoList) return
    const newSampleCosts: number[] = [] // 临时数组，用于存储计算后的 sampleCosts

    for (let index = 0; index < value?.videoSampleInfoList.length; index++) {
      const item = value?.videoSampleInfoList[index]
      try {
        formRefs.current[index]?.setFieldsValue({
          singleSampleCost: item?.singleSampleCost || 0,
          sampleNum: item?.sampleNum || 0,
          sampleCost: item?.singleSampleCost * item?.sampleNum
        })

        // 更新 sampleCosts
        const sampleCost = item?.singleSampleCost * item?.sampleNum
        newSampleCosts[index] = sampleCost
        setSampleCosts([...newSampleCosts])
        // 实时计算并更新状态
        const totalSampleCost = newSampleCosts.reduce((total, cost) => total + cost, 0)
        setSampleCostTotal(totalSampleCost)

        const totalSampleNum = value?.videoSampleInfoList.reduce((total: any, item: any) => {
          const sampleNum = item?.sampleNum || 0
          return total + sampleNum
        }, 0)

        setSampleNumTotal(totalSampleNum)
      } catch (error) {
        console.log(error)
      }
    }
  }

  const setFormRefsValues = async () => {
    if (!formRefs.current) {
      return
    }

    const newSampleCosts: number[] = [] // 临时数组，用于存储计算后的 sampleCosts
    const newSampleNum: number[] = []
    const promises = formRefs.current.map(async (form, index) => {
      try {
        if (form) {
          const sampleCost =
            form?.getFieldsValue()?.singleSampleCost * form?.getFieldsValue()?.sampleNum
          newSampleCosts[index] = sampleCost
          newSampleNum[index] = form?.getFieldsValue()?.sampleNum

          setSampleCosts([...newSampleCosts])
          // 实时计算并更新状态
          const totalSampleCost = newSampleCosts.reduce((total, cost) => total + cost, 0)
          setSampleCostTotal(totalSampleCost)
          const totalSampleNum = newSampleNum.reduce((total, cost) => total + cost, 0)

          // 更新样本总数
          setSampleNumTotal(totalSampleNum)
        }
      } catch (error) {
        console.log(error)
      }
    })

    // 等待所有表单的 setFieldsValue 完成
    await Promise.all(promises)
  }

  const modalReset = async () => {
    if (!formRefs.current) {
      return
    }
    const promises = formRefs.current.map(async (form, index) => {
      try {
        form?.resetFields()
      } catch (error) {
        console.log(error)
      }
    })
    // 等待所有表单的 setFieldsValue 完成
    await Promise.all(promises)

    setProductItem([])
    setSampleCosts([])
    setSampleNumTotal(0)
    setSampleCostTotal(0)
  }
  //获取寄样成本和数量
  const getSampleCostAndNum = () => {
    setRequestViable(true)
    getSampleCost({ videoId: item.videoId, shopCode: item.shopCode }).then((res) => {
      if (res.code == 200) {
        setProductItem(res.result)

        setFormValues(res.result)
      }
      setRequestViable(false)
    })
  }
  const handleClickOk = async () => {
    const sampleItems: any = []

    // 使用 Promise.all 等待所有表单的 validateFields 执行完毕
    const validatePromises = formRefs.current.map(async (form, index) => {
      setConfirmBtnViable(true)
      try {
        const values = await form?.validateFields() // 等待每个表单的验证完成
        const params = { ...values, productId: productItem.videoSampleInfoList[index].productId }
        if (form) {
          sampleItems[index] = params
        } else {
          sampleItems[index] = {
            sampleNum: productItem.videoSampleInfoList[index].sampleNum,
            singleSampleCost: productItem.videoSampleInfoList[index].singleSampleCost,
            productId: productItem.videoSampleInfoList[index].productId
          }
        }
      } catch (error) {
        console.log(error)
      }
    })

    // 等待所有表单验证完成
    await Promise.all(validatePromises)

    // 在所有表单验证完成后，提交数据
    const params = { sampleItems, videoId: item.videoId, shopCode: item.shopCode }

    // 提交后端 API 请求
    editSampleNum(params).then((res) => {
      if (res.code === 200 && res.result) {
        message.success(intlTrans('common.OperationSuccessful'))
        handleOk(params)
        modalReset()
      } else {
        message.error(intlTrans('common.OperationFailed'))
        handleOk(params)
      }
      setConfirmBtnViable(false)
    })
  }

  useEffect(() => {
    if (item.videoId && isEditModalOpen) {
      getSampleCostAndNum()
    }
  }, [item.videoId, isEditModalOpen])

  const handleClickSet = (item: any) => {
    window.open('/commodity/manage?productId=' + item.productId)
  }
  return (
    <Modal
      title={<div className={styles.modal_box}>{intlTrans('analytics.样品成本')}</div>}
      open={isEditModalOpen}
      onOk={handleClickOk}
      cancelText={intlTrans('common.Cancel')}
      okText={intlTrans('common.Sure')}
      width="800px"
      onCancel={() => {
        handleCancel()
        modalReset()
      }}
      confirmLoading={confirmBtnViable}
    >
      <Spin spinning={requestViable && isEditModalOpen} className={styles.spin_loading} />

      <Tabs tabPosition="left">
        {(productItem?.videoSampleInfoList || [])?.map((item: any, i: any) => {
          const id = String(i + 1)

          return (
            <Tabs.TabPane tab={`${intlTrans('commodity.商品')} ${id}`} key={id} forceRender>
              <Form
                key={i}
                ref={(el) => (formRefs.current[i] = el)}
                labelAlign="right"
                initialValues={{
                  singleSampleCost: item.singleSampleCost,
                  sampleNum: item.sampleNum
                }}
              >
                <div className={styles.single_item}>
                  <div className={styles.single_item_img}>
                    <img className={styles.single_item_img} src={item?.productCover} alt="" />
                  </div>
                  <div>
                    <div className={styles.product_box}>
                      <div className={styles.product_label}>{intlTrans('analytics.商品名称')}:</div>
                      <div className={styles.product_title}>{item.productTitle}</div>
                    </div>
                    <div className={styles.product_box}>
                      <div className={styles.product_label}>{intlTrans('analytics.商品ID')}:</div>
                      <div className={styles.product_title}>{item.productId || '-'}</div>
                    </div>
                    <div className={styles.product_box}>
                      <div className={styles.product_label}>{intlTrans('analytics.商品价格')}:</div>
                      <div className={styles.product_title}>
                        {item.minPrice || item.minPrice === 0 ? (
                          <span>{`${
                            item.minPrice == item.maxPrice
                              ? formatMoney(item.minPrice, true)
                              : `${formatMoney(item.minPrice, true)} - ${formatMoney(
                                  item.maxPrice,
                                  true
                                )}`
                          }`}</span>
                        ) : (
                          <div className={styles.center_box}>-</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={styles.input_box}>
                  <div className={styles.single_sample_cost_box}>
                    <Form.Item
                      label={intlTrans('analytics.单件寄样成本')}
                      name="singleSampleCost"
                      labelCol={{ span: 10 }}
                      wrapperCol={{ span: 16 }}
                    >
                      <InputNumber addonBefore="฿" disabled precision={2} />

                      {/* </div> */}
                    </Form.Item>
                    <Button type="link" onClick={() => handleClickSet(item)}>
                      {intlTrans('analytics.设置')}
                    </Button>
                  </div>
                  <Form.Item
                    label={intlTrans('analytics.本次寄样数量')}
                    name="sampleNum"
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 16 }}
                    rules={[{ required: true, message: intlTrans('analytics.输入内容不能为空') }]}
                  >
                    <InputNumber
                      min={0}
                      precision={0}
                      onChange={(e: any) => {
                        const sampleCost = e * item.singleSampleCost
                        sampleCosts[i] = sampleCost
                        setFormRefsValues()
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    label={intlTrans('analytics.本次寄样成本')}
                    name="SampleCost"
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {sampleCosts[i] ? formatMoney(sampleCosts[i], true) : formatMoney(0, true)}
                  </Form.Item>
                </div>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    {/* 提交 */}
                  </Button>
                </Form.Item>
              </Form>
            </Tabs.TabPane>
          )
        })}
      </Tabs>

      <div className={styles.total_box}>
        <div>
          {intlTrans('analytics.样品数量总计')}： {sampleNumTotal}
        </div>
        <div className={styles.sample_cost_total_box}>
          {intlTrans('analytics.样品总计成本')}：
          <span className={styles.sample_cost_total}>{formatMoney(sampleCostTotal, true)}</span>
        </div>
      </div>
    </Modal>
  )
}
