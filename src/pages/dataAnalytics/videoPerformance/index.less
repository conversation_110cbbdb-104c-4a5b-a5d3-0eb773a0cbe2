.commodity-warehouse-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.commodity-warehouse-table {
  .ant-table-header {
    position: sticky;
    top: 60px;
    z-index: 1000;
  }
  .ant-col-offset-9 {
    margin-left: 20%;
  }

  .ant-pro-form-query-filter {
    > .ant-row > .ant-col:last-child {
      flex: none;
      max-width: none;
      margin-left: 0 !important;
      padding-left: 0 !important;
      text-align: left !important;

      .ant-form-item-no-colon {
        display: none;
      }
    }
  }
}
.btn_box {
  display: flex;
  gap: 8px;
  margin-bottom: 50px;
  margin-left: -124px;
}
.video_box {
  display: flex;
  // justify-content: space-between;
}
.product_box {
  display: flex;
  flex-wrap: wrap;
}
.product_img_box {
  margin-left: 10px;
}
.title_box {
  display: flex;
}
.video_duration {
  color: #958686;
  word-break: break-all;
}
.video_id {
  color: #958686;
  word-break: break-all;
  //  font-size: 12px;
}
.video_content {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: start;
  justify-content: space-between;
  margin-left: 8px;
}
.video_time {
  display: inline;
  color: #958686;
  text-align: left;
}
.master_box {
  display: flex;
}
.master_content {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
  word-break: break-all;
}
.master_uniqueName {
  color: #958686;
  font-size: 14px;
  text-align: left;
}
.edit_btn {
  margin-left: 8px;
  color: #009995;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
}
.disabled_btn {
  margin-left: 8px;
  color: #ccc;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
}
.item_box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.video_title {
  display: -webkit-box; /* 结合以下属性，控制行数 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-align: left;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-line-clamp: 1; /* 显示 3 行文本 */
  -webkit-box-orient: vertical;
}
.item_word {
  word-wrap: break-word;
  word-break: break-all;
}
.icon_play {
  position: absolute;
  top: 15%;
  left: 15%;
  z-index: 8;
  width: 32px;
  height: 32px;
}
.product_img {
  position: relative;
  cursor: pointer;
}
.center_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}
.no_data {
  display: flex;
  align-items: center;
  padding-left: 100px;
}

.custom_month_picker {
  :global {
    .ant-picker-year-panel .ant-picker-cell-inner,
    .ant-picker-quarter-panel .ant-picker-cell-inner,
    .ant-picker-month-panel .ant-picker-cell-inner {
      width: auto !important;
    }
  }
}
