import LockDemo from '@/components/LockDemo'
import VideoPerContent from './components/VideoPerContent'
import { useQuery } from '@/hooks'
import { useEffect, useRef, useState } from 'react'
import { useHistory } from 'umi'
import { suppliersInfo } from '@/api/analytics'
import Loading from '@/loading'
import { isAuthShopStorage } from '@/utils/storage'
import { message } from 'antd'
import { intlTrans } from '@/utils/common'
import useSampleReview from '@/hooks/useSampleReview'
const VideoPerformance = () => {
  const queryParams = useQuery()
  const history = useHistory()
  const [isShowDemo, setIsShowDemo] = useState<any>(undefined)

  function closeParentPage() {
    if (window.opener) {
      window.opener.close() // 关闭原页面
    }
  }
  useSampleReview()
  const isHaveAuthShop = () => {
    suppliersInfo().then((res) => {
      if (res.code == 200) {
        const { isAuthShop } = res.result
        setIsShowDemo(!isAuthShop)
        if (isAuthShop) {
          isAuthShopStorage.set('true')
        } else {
          isAuthShopStorage.set('false')
        }
      } else {
        setIsShowDemo(true)
      }
    })
  }
  useEffect(() => {
    if (
      Object.keys(queryParams).length == 0 ||
      (queryParams && !queryParams.isDemo && queryParams.status == '1')
    ) {
      isHaveAuthShop()
    }
  }, [])
  useEffect(() => {
    const { status, errorMsg, isDemo } = queryParams
    let url = '/dataAnalytics/videoPerformance'
    if (isDemo) {
      setIsShowDemo(false)
      url = '/dataAnalytics/videoPerformance?isDemo=true'
    }
    if (status === '0' && errorMsg) {
      message.error(errorMsg)
      history.push(url)
      closeParentPage()
    }
    if (status === '1') {
      message.success(intlTrans('locale.授权成功'))
      history.push(url)
      closeParentPage()
    }
  }, [])

  return (
    <>
      {isShowDemo != undefined ? (
        !isShowDemo ? (
          <VideoPerContent />
        ) : (
          <LockDemo
            type={2}
            onShowDemo={() => {
              setIsShowDemo(false)
              history.push('/dataAnalytics/videoPerformance?isDemo=true')
            }}
          />
        )
      ) : (
        Loading()
      )}
    </>
  )
}

export default VideoPerformance
