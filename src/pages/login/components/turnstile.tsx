import { isBuildProduction } from '@/utils/common'
import { getTurnstileKey } from '@/utils/getBaseUrl'
import { useEffect } from 'react'
import Turnstile from 'react-turnstile'

interface Props {
  onVerify: (token: string) => void
}

const TurnstileWidget = ({ onVerify }: Props) => {
  useEffect(() => {
    if (!isBuildProduction) {
      onVerify('666')
    }
  }, [])
  return isBuildProduction ? <Turnstile sitekey={getTurnstileKey()} onVerify={onVerify} /> : null
}

export default TurnstileWidget
