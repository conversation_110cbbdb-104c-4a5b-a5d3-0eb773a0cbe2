.login {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding-bottom: 100px;
  background-color: rgb(240, 243, 246);
  background-image: url('../../assets/images/login/login_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-logo {
    width: 300px;
    height: 80px;
    margin-top: 100px;
    margin-bottom: 24px;
  }

  &-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 400px;
    padding: 40px;
    background: #fbfbff;
    border-radius: 10px;
    box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.04);

    &-title {
      width: 100%;
      margin-bottom: 48px;
      color: #181818;
      font-weight: bold;
      font-size: 24px;
      line-height: 33px;
      text-align: center;
    }

    &-button-wrap {
      display: flex;
      justify-content: center;
      padding-top: 24px;
    }

    &-button {
      width: 100%;
      height: 50px;
      border-radius: 4px;
    }

    &-formitem {
      margin-bottom: 34px;
    }

    &-inputlabel {
      color: #181818;
      font-weight: bold;
      font-size: 18px;
    }

    &-input {
      padding: 10px 0;
      font-size: 16px;
      border-bottom: 1px solid #dde2f1;
    }

    &-input:hover {
      border-bottom: 1px solid #dde2f1 !important;
    }
  }
}
