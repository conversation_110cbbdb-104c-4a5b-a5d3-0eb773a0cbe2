/**
 * 登录页面
 */
import type { IOatuthTokenParamsProps } from '@/api/user'
import userApi from '@/api/user'
import logo from '@/assets/images/login/logo.png'
import { uchoiceToken } from '@/utils/auth'
import { intlTrans, isBuildProduction } from '@/utils/common'
import { Button, Col, Form, Image, Input, Select, message } from 'antd'
import qs from 'qs'
import { useState, useEffect } from 'react'
import { setLocale, useHistory, useIntl } from 'umi'
import styles from './index.less'
import TurnstileWidget from './components/turnstile'
import { useTurnstile } from 'react-turnstile'
import {
  isAuthShopStorage,
  isToAuditCountStorage,
  nationStorage,
  userInfoStorage
} from '@/utils/storage'
import { suppliersInfo } from '@/api/analytics'
import { ordersStats } from '@/api/sample'

function Index() {
  const intl = useIntl()
  const history = useHistory()

  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const turnstile = useTurnstile()

  useEffect(() => {
    form.setFieldsValue({
      ...form.getFieldsValue(),
      nation: nationStorage.get() || 'TH'
    })
  }, [])
  const onIsHaveAuthShop = () => {
    suppliersInfo().then((res) => {
      if (res.code == 200) {
        const { isAuthShop } = res.result
        if (isAuthShop) {
          isAuthShopStorage.set('true')
        } else {
          isAuthShopStorage.set('false')
        }
      } else {
        isAuthShopStorage.set('false')
      }
    })
  }

  const onOrderStatus = async () => {
    const params: any = {
      status: 0
    }
    const res = await ordersStats(params)
    if (res.code === 200) {
      const { toBeAuditCount } = res.result
      if (toBeAuditCount) {
        isToAuditCountStorage.set('true')
      } else {
        isToAuditCountStorage.set('false')
      }
    }
  }
  const fetchUserInfo = async () => {
    try {
      const data = await userApi.usersCurrent()
      setLoading(true)
      if (data.code === 200) {
        userInfoStorage.set(JSON.stringify(data.data))
        const parse: any = qs.parse(
          window.location.href.indexOf('?') !== -1
            ? window.location.href.split('?')[1]
            : window.location.href
        )

        if (parse.redirect) {
          location.href = decodeURIComponent(parse.redirect)
        } else {
          history.push({
            pathname: '/'
          })
        }

        onIsHaveAuthShop()
        onOrderStatus()
      }
    } catch (error: any) {
      setLoading(false)
      message.error(error)
    }
  }

  const onVerify = (token: string) => {
    form.setFieldValue('cloudflare_token', token)
  }

  const verifyFailed = () => {
    form.setFieldValue('cloudflare_token', null)

    if (isBuildProduction) {
      turnstile.reset()
    }
  }

  const onSubmit = async ({ username, password, nation }: IOatuthTokenParamsProps) => {
    try {
      setLoading(true)
      uchoiceToken.remove()
      const data = await userApi.oatuthToken({
        username: username,
        password,
        grantType: 'PASSWORD',
        cloudflareToken: form.getFieldValue('cloudflare_token'),
        accountType: 'YOUPIK_GROW'
      })

      if (data.code === 200) {
        // setDefalutLang();
        nationStorage.set(form.getFieldsValue().nation || 'TH')
        // setLanguage(form.getFieldsValue().nation);
        uchoiceToken.set(data.data.token)
        fetchUserInfo()
      } else {
        verifyFailed()
      }
    } catch (error: any) {
      verifyFailed()
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={styles['login']}>
      <Image className={styles['login-logo']} src={logo} preview={false} />
      <div className={styles['login-card']}>
        <p className={styles['login-card-title']}>{intlTrans('login.logIn')}</p>
        <Form
          style={{ width: '100%' }}
          form={form}
          layout="vertical"
          wrapperCol={{ span: 12 }}
          onFinish={onSubmit}
        >
          <span className={styles['login-card-inputlabel']}>{intlTrans('login.username')}</span>
          <Form.Item
            className={styles['login-card-formitem']}
            name="username"
            wrapperCol={{ span: 24 }}
            rules={[{ required: true, message: '请输入账号' }]}
          >
            <Input
              className={styles['login-card-input']}
              allowClear
              bordered={false}
              placeholder={intlTrans('login.username')}
              maxLength={50}
            />
          </Form.Item>

          <span className={styles['login-card-inputlabel']}>{intlTrans('login.password')}</span>
          <Form.Item
            className={styles['login-card-formitem']}
            name="password"
            wrapperCol={{ span: 24 }}
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              className={styles['login-card-input']}
              allowClear
              bordered={false}
              placeholder={intlTrans('login.password')}
              maxLength={50}
            />
          </Form.Item>

          <span className={styles['login-card-inputlabel']}>
            {intl.formatMessage({ id: 'locale.nation' })}
          </span>
          <Form.Item
            style={{ marginTop: '10px' }}
            className={styles['login-card-formitem']}
            name="nation"
            wrapperCol={{ span: 24 }}
            rules={[{ required: true }]}
          >
            <Select
              onChange={(e) => {
                nationStorage.set(e)
              }}
              allowClear
            >
              <Select.Option value="TH" className={styles['login-select-nation']}>
                🇹🇭 {intl.formatMessage({ id: 'locale.泰国' })}
              </Select.Option>
              <Select.Option value="VI">
                🇻🇳 {intl.formatMessage({ id: 'locale.越南' })}
              </Select.Option>
            </Select>
          </Form.Item>

          <Button htmlType="submit" />
        </Form>

        <Form.Item name="cloudflare_token" hidden rules={[{ required: true, message: '' }]}>
          <input />
        </Form.Item>

        <TurnstileWidget onVerify={onVerify} />

        <div className={styles['login-card-button-wrap']}>
          <Button
            className={styles['login-card-button']}
            type="primary"
            size={'large'}
            loading={loading}
            onClick={() => {
              form.submit()
            }}
          >
            {intlTrans('login.logIn')}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Index
