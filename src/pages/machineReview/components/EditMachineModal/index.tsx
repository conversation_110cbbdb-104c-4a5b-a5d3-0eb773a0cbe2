import React, { useEffect, useRef, useState } from 'react'
import { Modal, Form, Switch, Radio, InputNumber, RadioChangeEvent } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { CheckboxOptionType } from 'antd/es/checkbox'
import TemplateContent from '../TemplateContent'
import { useIntl } from 'umi'
import shopApi from '@/api/shop'
import { formatNum } from '@/utils/format'
import styles from './index.less'

const { confirm } = Modal

interface IEditMachineModalProps {
  enable: boolean
  visible: boolean
  onClose: () => void
  onConfirm: () => void
  currentRecord: any
}

const RADICAL = '2';
const CONSERVATIVE = '3';
const CUSTOM = '1';

const EditMachineModal: React.FC<IEditMachineModalProps> = ({
  enable,
  currentRecord,
  visible,
  onClose,
  onConfirm
}) => {
  const intl = useIntl()
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const formRef = useRef<any>()
  const [machineReviewEnable, setMachineReviewEnable] = useState<boolean>(false)

  const options: CheckboxOptionType[] = [
    { label: `🔥${intl.formatMessage({ id: 'account.模板' })}1: ${intl.formatMessage({ id: 'account.快速出单型' })}`, value: RADICAL },
    { label: `${intl.formatMessage({ id: 'account.模板' })}2: ${intl.formatMessage({ id: 'account.防踩雷保守型' })}`, value: CONSERVATIVE },
    { label: intl.formatMessage({ id: 'account.自定义' }), value: CUSTOM },
  ];

  const [reviewType, setReviewType] = useState<string>(RADICAL)

  useEffect(() => {
    if (!visible) {
      formRef.current?.resetFields()
      setConfirmLoading(false)
      setReviewType(RADICAL)
      setMachineReviewEnable(false)
    } else {
      formRef.current?.setFieldsValue({
        machineReviewEnable: currentRecord.orderAudit === 1,
        salesCount: currentRecord.salesThreshold || currentRecord.systemSalesThreshold,
        followers: currentRecord.fansThreshold ? currentRecord.fansThreshold : currentRecord.systemFansThreshold,
      })
      setReviewType(`${currentRecord.type || RADICAL}`)
      setMachineReviewEnable(currentRecord.orderAudit === 1)
    }
  }, [visible, currentRecord])

  const handleConfirm = () => {
    const machineReviewEnable = formRef.current?.getFieldValue('machineReviewEnable')
    formRef.current?.validateFields().then((values: any) => {
      if (enable && !machineReviewEnable) {
        onClose()
        return
      }
      confirm({
        title: intl
          .formatMessage({ id: 'account.确认样品机审通过条件' })
          .replace('{{word}}', currentRecord?.shopName),
        icon: <ExclamationCircleFilled />,
        content: (
          <div
            dangerouslySetInnerHTML={{
              __html: machineReviewEnable
                ? intl.formatMessage({
                  id: 'account.生效后符合通过条件的达人样品申请将自动通过商家审核你还要继续吗'
                })
                : intl.formatMessage({ id: 'account.关闭后所有样品将由商家手动审核你还要继续吗' })
            }}
          ></div>
        ),
        okText: intl.formatMessage({ id: 'account.确定' }),
        cancelText: intl.formatMessage({ id: 'account.取消' }),
        onOk() {
          saveMachineReview(values)
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    })
  }

  const saveMachineReview = async (values: any) => {
    const { machineReviewEnable, salesCount, followers, } = values
    try {
      setConfirmLoading(true)
      const postData: any = {
        shopCode: currentRecord.shopCode, // 店铺编码
        orderAudit: machineReviewEnable ? 1 : 0, // 订单自动审核
        type: reviewType,
      }
      if (reviewType === RADICAL) {
        postData.salesThreshold = 50;
        postData.fansThreshold = 1000;
      }
      if (reviewType === CONSERVATIVE) {
        postData.fulfillmentRate = 0.8;
        postData.salesThreshold = 1000;
        postData.fansThreshold = 10000;
      }
      if (reviewType === CUSTOM) {
        postData.salesThreshold = salesCount;
        postData.fansThreshold = followers;
      }
      const result = await shopApi.updateAutoReview(postData)
      if (result.code === 200) {
        setConfirmLoading(false)
        onConfirm()
      }
    } catch (error) {
      setConfirmLoading(false)
    }
  }

  return (
    <Modal
      title={intl
        .formatMessage({ id: 'account.设置样品机审通过条件' }, { word: currentRecord?.shopName })}
      open={visible}
      onOk={handleConfirm}
      onCancel={onClose}
      width={880}
      okText={intl.formatMessage({ id: 'account.确定' })}
      cancelText={intl.formatMessage({ id: 'account.取消' })}
      confirmLoading={confirmLoading}
    >
      <Form labelWrap labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} layout="horizontal" ref={formRef}>
        <Form.Item
          label={intl.formatMessage({ id: 'account.机审开通状态' })}
          name="machineReviewEnable"
          valuePropName="checked"
          extra={<div dangerouslySetInnerHTML={{ __html: intl.formatMessage({ id: 'account.system_review_enable_extra' }) }}></div>}
        >
          <Switch onChange={(value) => {
            setMachineReviewEnable(value)
          }} />
        </Form.Item>
        {machineReviewEnable && <>
          <Form.Item label={<div></div>} colon={false}>
            <Radio.Group options={options} value={reviewType} onChange={(e: RadioChangeEvent) => {
              setReviewType(e.target.value)
            }} />
          </Form.Item>
          {reviewType === RADICAL && <Form.Item label={<div></div>} colon={false}>
            <TemplateContent type="radical" follower={1000} monthSales={50} orderRate={70}></TemplateContent>
          </Form.Item>}
          {reviewType === CONSERVATIVE && <Form.Item label={<div></div>} colon={false}>
            <TemplateContent type="conservative" follower={10000} monthSales={1000} performanceRate={80}></TemplateContent>
          </Form.Item>}
          {reviewType === CUSTOM && <Form.Item
            label={intl.formatMessage({ id: 'account.销量条件' })}
            extra={`${intl.formatMessage({ id: 'account.系统参考值' })}: ${formatNum(currentRecord?.systemSalesThreshold)}`}
          >
            <Form.Item
              name="salesCount"
              noStyle
              rules={[{ required: true, message: intl.formatMessage({ id: 'common.PleaseEnter' }) }]}
            >
              <InputNumber
                style={{ width: 200 }}
                min="0"
                max="*********"
                parser={(value) => value!.replace(/\D/g, '')}
              ></InputNumber>
            </Form.Item>
            <span className="ant-form-text">{intl.formatMessage({ id: 'account.件' })}</span>
          </Form.Item>}
          {reviewType === CUSTOM && <Form.Item
            label={intl.formatMessage({ id: 'account.粉丝数条件' })}
            extra={`${intl.formatMessage({ id: 'account.系统参考值' })}: ${formatNum(currentRecord?.systemFansThreshold)}`}
          >
            <Form.Item
              name="followers"
              noStyle
              rules={[{ required: true, message: intl.formatMessage({ id: 'common.PleaseEnter' }) }]}
            >
              <InputNumber
                style={{ width: 200 }}
                min="0"
                max="*********"
                parser={(value) => value!.replace(/\D/g, '')}
              ></InputNumber>
            </Form.Item>
          </Form.Item>}
        </>}
      </Form>
    </Modal>
  )
}

export default EditMachineModal
