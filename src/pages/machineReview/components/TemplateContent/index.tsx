import React from 'react'
import { useIntl } from 'umi'
import { formatNum } from '@/utils/format';
import styles from './index.less'

interface ITemplateContentProps {
  type?: 'radical' | 'conservative';
  follower?: number;
  monthSales?: number;
  orderRate?: number;
  performanceRate?: number; // 履约率
}

const TemplateContent: React.FC<ITemplateContentProps> = ({ follower, monthSales, orderRate, performanceRate, type }) => {
  const intl = useIntl()

  return (
    <div>
      <div>{intl.formatMessage({ id: 'account.默认条件' })}:</div>
      <ul>
        {follower !== undefined && <li>{`${intl.formatMessage({ id: 'account.粉丝量' })} ≥ ${formatNum(follower)}`}</li>}
        {monthSales !== undefined && <li>{intl.formatMessage({ id: 'account.近30天月销' }).replaceAll("{{num}}", formatNum(monthSales),)}</li>}
        {orderRate !== undefined && <li>{`${intl.formatMessage({ id: 'account.出单率' })}≥${orderRate}% (${intl.formatMessage({ id: 'account.达人样品申请-发布带货内容-出单计算' })})`}</li>}
        {performanceRate !== undefined && <li>{`${intl.formatMessage({ id: 'account.履约率' })} ≥ ${performanceRate}% (${intl.formatMessage({ id: 'account.通过达人历史样品申请-发布带货内容计算' })})`}</li>}
      </ul>

      {type === 'radical' && <div className={styles.tip_container}>
        <div>{intl.formatMessage({ id: 'account.目标：尽快跑出订单，有明确带货能力' })}</div>
        <div>{intl.formatMessage({ id: 'account.适用场景：新品测试、促销爆量、清库存' })}</div>
      </div>}
      {type === 'conservative' && <div className={styles.tip_container}>
        <div>{intl.formatMessage({ id: 'account.目标：只投信得过、转化能力强的达人' })}</div>
        <div>{intl.formatMessage({ id: 'account.适用场景：怕浪费样品，想先从优质达人入手' })}</div>
      </div>}
    </div>
  )
}

export default TemplateContent
