import React, { useEffect, useState } from 'react'
import { Card, Table, Space } from 'antd'
import { uniqueId } from 'lodash'
import type { ColumnsType } from 'antd/es/table'
import { useIntl } from 'umi'
import { formatNumber } from '@/utils/common'
import EditMachineModal from './components/EditMachineModal'
import shopApi from '@/api/shop'
import useShopAuthLock from '@/hooks/useShopAuthLock'
import styles from './index.less'
import useSampleReview from '@/hooks/useSampleReview'

const MachineReview = () => {
  const intl = useIntl()
  const [dataSource, setDataSource] = useState<any>([])
  const [fetchLoading, setFetchLoading] = useState<boolean>(false)
  const [editVisible, setEditVisible] = useState<boolean>(false)
  const [currentRecord, setCurrentRecord] = useState<any>()
  const [enable, setEnable] = useState<boolean>(false)

  useShopAuthLock()
  useSampleReview()

  useEffect(() => {
    getDataSource()
  }, [])

  const getDataSource = async () => {
    setFetchLoading(true)
    try {
      const params = {}
      const result = await shopApi.getAutoReviewList(params)
      if (result.code == 200 && result.result) {
        setDataSource(result.result)
      }
      setFetchLoading(false)
    } catch (error) {
      setFetchLoading(false)
    }
  }

  const columns: ColumnsType<any> = [
    {
      title: intl.formatMessage({ id: 'account.TikTok店铺' }),
      key: 'firstColumn',
      align: 'center',
      render(value, record, index) {
        return (
          <div className={styles.shop_info_container}>
            {/* <img className={styles.shop_image} src={record.shopImage}></img> */}
            <div className={styles.shop_content_container}>
              <div className={styles.shop_name}>{record.shopName}</div>
              <div className={styles.shop_code}>{`${intl.formatMessage({
                id: 'account.店铺短码'
              })}:${record.shopCode}`}</div>
            </div>
          </div>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'account.机审' }),
      key: 'machineReview',
      align: 'center',
      render: (value, record, index) => {
        const { orderAudit } = record

        return (
          <div className={styles.machine_review_container}>
            <div className={styles.machine_review_status_container}>
              <div style={{ color: orderAudit === 1 ? '#009995' : '#999' }}>
                {intl.formatMessage({
                  id: orderAudit === 1 ? 'account.已开启' : 'account.未开启'
                })}
              </div>
              <div
                className={styles.machine_review_status_dot}
                style={{ backgroundColor: orderAudit === 1 ? '#009995' : 'red' }}
              />
            </div>
            <div className={styles.machine_review_desc}>{`(${intl.formatMessage({
              id: 'account.销量'
            })}:${formatNumber(
              record.salesThreshold || record.systemSalesThreshold
            )}, ${intl.formatMessage({
              id: 'account.粉丝'
            })}:${formatNumber(record.fansThreshold || record.systemFansThreshold)})`}</div>
          </div>
        )
      }
    },
    {
      title: intl.formatMessage({ id: 'account.操作' }),
      key: 'action',
      align: 'center',
      render: (value, record, index) => {
        const { orderAudit } = record
        return orderAudit === 1 ? (
          <a style={{ color: '#999999' }} href="" onClick={(e) => handleEditMachineReview(record, false, e)}>
            {intl.formatMessage({ id: 'account.编辑机审条件' })}
          </a>
        ) : (
          <a href="" onClick={(e) => handleEditMachineReview(record, true, e)}>
            {intl.formatMessage({ id: 'account.开通机审' })}
          </a>
        )
      }
    }
  ]

  const handleEditMachineReview = (record: any, enable: boolean, e: any) => {
    e.preventDefault()
    setEnable(enable)
    setCurrentRecord(record)
    setEditVisible(true)
  }

  return (
    <>
      <div className={styles.header}> {intl.formatMessage({ id: 'account.样品申请机审配置' })}</div>
      <Space direction='vertical' style={{ width: '100%', }}>
        <Card className={`${styles['m-list']}`} bordered={false}>
          <Table
            rowKey={(record) => uniqueId()}
            columns={columns}
            dataSource={dataSource}
            loading={fetchLoading}
            pagination={false}
          />
        </Card>
        <Card title={intl.formatMessage({ id: 'account.机审提示' })}>
          <ul>
            <li>
              {intl.formatMessage({ id: 'account.system_review_tip1' })}
            </li>
            <li dangerouslySetInnerHTML={{ __html: intl.formatMessage({ id: 'account.system_review_tip2' }).replaceAll('{{content}}', `<a href="/commodity/cooperate">${intl.formatMessage({ id: 'account.Youpik合作商品' })}</a>`) }} />
          </ul>
        </Card>
      </Space>
      <EditMachineModal
        enable={enable}
        currentRecord={currentRecord}
        visible={editVisible}
        onClose={() => setEditVisible(false)}
        onConfirm={() => {
          setEditVisible(false)
          getDataSource()
        }}
      />
    </>
  )
}

export default MachineReview
