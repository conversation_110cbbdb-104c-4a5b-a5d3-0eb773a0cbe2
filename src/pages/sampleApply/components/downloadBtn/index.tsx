import { DownloadRecordsModal } from '@/components/DownloadRecordsModal'
import { intlTrans } from '@/utils/common'
import { useBoolean } from 'ahooks'
import { Button, message, Select, Tooltip } from 'antd'
import { useEffect, useRef, useState } from 'react'
import styles from './index.less'
import { exportOrders, hasNewMessages, importTrackingNumbers, markMessagesRead } from '@/api/sample'
import { DownloadTabRecordsModal } from '@/components/DownloadTabRecordsModal'
import { searchType } from '../sampleComPart'

interface Props {
  //   downloadRequest: () => Promise<IResponseProps<boolean>>
  childrenType: number
  params: any
  onOk: () => void
}
export const DownLoadBtn = ({ childrenType, params, onOk }: Props) => {
  const [downloadRecordModalOpen, { toggle: downloadRecordModalOpenToggle }] = useBoolean()
  const [importLoading, setImportLoading] = useState<boolean>(false)
  const fileInputRef = useRef<any>(null)
  const [isRead, setIsRead] = useState<boolean>(false)

  const onHasNewMessages = () => {
    hasNewMessages().then((res) => {
      if (res.result) {
        setIsRead(res.result)
      }
    })
  }
  const onMarkMessagesRead = () => {
    markMessagesRead().then((res) => {
      if (res.code == 200) {
        setIsRead(false)
      }
    })
  }
  useEffect(() => {
    onHasNewMessages()
  }, [])
  //导出订单
  const exportOrderNo = async () => {
    const form = params.getFieldsValue()
    const queryParams = {
      status: '2001',
      pageNo: 1,
      pageSize: 10,
      ...(await searchType(form))
    }
    const res = await exportOrders(queryParams)
    if (res.code === 200) {
      onHasNewMessages()
      message.success(intlTrans('common.导出成功请在历史记录里查看'))
    } else {
      message.success(intlTrans('common.OperationFailed'))
    }
  }
  //导入运单号
  const handleFileChange = (e: any) => {
    const selectedFile = e.target.files[0]
    if (!selectedFile) {
      return
    }
    setImportLoading(true)
    if (selectedFile) {
      const formData = new FormData()
      formData.append('file', selectedFile)
      importTrackingNumbers(formData)
        .then((res) => {
          if (res && res.code === 200) {
            onHasNewMessages()
            message.success(intlTrans('common.导入成功请在历史记录里查看'))
            onOk()
          } else {
            message.success(intlTrans('common.OperationFailed'))
          }
        })
        .catch((err) => {
          console.log(err)
        })
        .finally(() => {
          setImportLoading(false)
        })
    }
  }

  const handleSelect = (e: any) => {
    if (e == 1) {
      exportOrderNo()
    } else {
      fileInputRef.current.value = ''
      fileInputRef.current?.click()
    }
  }

  return (
    <>
      <div style={{ display: 'flex' }}>
        <Select
          loading={importLoading}
          className={styles.placeholder}
          value={intlTrans('sample.线下发货')}
          placeholder={intlTrans('sample.线下发货')}
          onChange={handleSelect}
          options={[
            {
              value: '1',
              label: (
                <Tooltip title={intlTrans('sample.导出线下订单')}>
                  {intlTrans('sample.导出线下订单')}
                </Tooltip>
              )
            },
            {
              value: '2',
              label: (
                <Tooltip title={intlTrans('sample.导入运单号')}>
                  {intlTrans('sample.导入运单号')}
                </Tooltip>
              )
            }
          ]}
        />
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileChange}
          accept=".xls, .xlsx"
        />
        <div style={{ width: 12 }} />
        <Button
          onClick={() => {
            onMarkMessagesRead()
            downloadRecordModalOpenToggle()
          }}
        >
          {intlTrans('common.历史记录')}
          {isRead && <span className={styles.icon_red} />}{' '}
        </Button>
      </div>
      <DownloadTabRecordsModal
        open={downloadRecordModalOpen}
        onCancel={downloadRecordModalOpenToggle}
      />
    </>
  )
}
