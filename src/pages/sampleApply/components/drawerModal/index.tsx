import { PlusOutlined } from '@ant-design/icons'
import { Button, Col, DatePicker, Drawer, Form, Input, Row, Select, Space } from 'antd'
import React, { useState } from 'react'
import styles from './index.less'
import { intlTrans } from '@/utils/common'
interface Props {
  isDrawerModalOpen: boolean
  showDrawer: (e: any, value: any) => void
  handleFilterCancel: () => void
}
const DrawerModal = ({ isDrawerModalOpen, showDrawer, handleFilterCancel }: Props) => {
  const [form] = Form.useForm()
  function countNonEmptyValues(obj: any) {
    return Object.values(obj).filter(
      (value) => value !== '' && value !== null && value !== undefined
    ).length
  }

  const handleClickOk = (values?: any) => {
    const formValue = form.getFieldsValue()
    const filterValue = countNonEmptyValues(formValue)
    showDrawer(formValue, filterValue)
  }
  return (
    <Drawer
      mask={false}
      title={intlTrans('sample.筛选')}
      onClose={() => {
        handleFilterCancel()
      }}
      open={isDrawerModalOpen}
      className={styles.drawer_box}
      bodyStyle={{ paddingBottom: 80 }}
      footer={
        <Space>
          <Button
            type="link"
            onClick={() => {
              form.resetFields()
              handleClickOk()
            }}
          >
            {intlTrans('sample.重置筛选')}
          </Button>
          <Button
            onClick={() => {
              handleFilterCancel()
            }}
            type="default"
          >
            {intlTrans('common.Cancel')}
          </Button>
          <Button onClick={handleClickOk} type="primary">
            {intlTrans('sample.筛选')}
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        className={styles.container}
        name="form"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onFinish={(values) => {
          handleClickOk(values)
        }}
      >
        <Form.Item name="minFans" label={`${intlTrans('sample.最低粉丝量')}`}>
          <div className={styles.form_box}>
            {' '}
            <span style={{ color: 'red' }}> ≥</span> &nbsp; <Input />
            <span className={styles.space_left}>{intlTrans('sample.个')}</span>
          </div>
        </Form.Item>
        <Form.Item name="minDeal" label={`${intlTrans('sample.最低月销量')}`}>
          <div className={styles.form_box}>
            <span style={{ color: 'red' }}> ≥</span> &nbsp; <Input />
            <span className={styles.space_left}>{intlTrans('sample.件')}</span>
          </div>
        </Form.Item>
        <Form.Item name="minPerformance" label={`${intlTrans('sample.历史履约率')}`}>
          <div className={styles.form_box}>
            <span style={{ color: 'red' }}> ≥</span> &nbsp;
            <Input />
            <span className={styles.space_left}>{intlTrans('sample.百分号')}</span>
          </div>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default DrawerModal
