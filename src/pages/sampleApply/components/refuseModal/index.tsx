import styles from './index.less'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { intlTrans, replaceExceptNumber } from '@/utils/common'
import { Button, Form, Input, message, Modal, Select } from 'antd'
import { rejectionReasons } from '@/api/sample'
import { onOrderNoReview } from '../sampleComPart'

interface Props {
  isRefuseModalOpen: boolean
  handleOk: (e: any) => void
  handleCancel: () => void
  item: any
  setIsRefuseModalOpen: any
}

export default function RefuseModal({
  isRefuseModalOpen,
  handleCancel,
  handleOk,
  item,
  setIsRefuseModalOpen
}: Props) {
  const [form] = Form.useForm()
  const reasonCode = Form.useWatch('reasonCode', form)
  const [confirmBtnViable, setConfirmBtnViable] = useState(false)
  const [rejectionReason, setRejectionReason] = useState([])
  const handleRefuseList = () => {
    rejectionReasons().then((res: any) => {
      if (res.code == 200) {
        const reasons = res.result.map((item: any) => ({
          label: item.reason,
          value: item.code
        }))
        setRejectionReason(reasons)
      }
    })
  }
  useEffect(() => {
    if (item && isRefuseModalOpen) {
      handleRefuseList()
    }
  }, [item])
  const handleClickOk = async () => {
    form
      .validateFields()
      .then(async (values) => {
        // setConfirmBtnViable(true)
        const param = {
          orderNo: item.orderNo,
          rejectReasonCode: (reasonCode || []).join(', '),
          action: 2
        }
        const result = await onOrderNoReview(param)
        if (result) {
          handleOk(param)
        }
        setConfirmBtnViable(false)
      })
      .catch((errorInfo) => {
        console.log(errorInfo)
      })
    setConfirmBtnViable(false)
    setIsRefuseModalOpen(false)
    form.resetFields()
  }

  return (
    <Modal
      title={<div className={styles.modal_box}>{intlTrans('sample.样品审核拒绝')}</div>}
      cancelText={intlTrans('common.Cancel')}
      okText={intlTrans('common.Sure')}
      open={isRefuseModalOpen}
      onOk={handleClickOk}
      onCancel={() => {
        form.resetFields()
        handleCancel()
      }}
      confirmLoading={confirmBtnViable}
    >
      <Form
        form={form}
        name="form"
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        onFinish={(values) => {
          handleClickOk()
        }}
      >
        <Form.Item
          name="reasonCode"
          label={`${intlTrans('sample.拒绝理由')}(${intlTrans('sample.多选')})`}
          rules={[{ required: true }]}
        >
          <Select
            placeholder={intlTrans('sample.拒绝理由')}
            showArrow
            mode="multiple"
            options={rejectionReason}
          />
        </Form.Item>
        {/* {reasonCode == '0000' && (
          <Form.Item
            name="otherReason"
            label={intlTrans('sample.其他原因填写')}
            rules={[{ required: true }]}
          >
            <Input.TextArea rows={4} placeholder={intlTrans('sample.其他原因填写')} />
          </Form.Item>
        )} */}
        <Form.Item>
          <Button type="primary" htmlType="submit" />
        </Form.Item>
      </Form>
    </Modal>
  )
}
