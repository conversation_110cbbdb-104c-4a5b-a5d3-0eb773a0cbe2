import { batchReview, orderNoReview, ordersStats } from '@/api/sample'
import { intlTrans } from '@/utils/common'
import { Form, Input, message, Select, Tag } from 'antd'
import React from 'react'

export const handleSelectChange = (value: any, setPlaceholder: any) => {
  let newPlaceholder = intlTrans('common.PleaseEnter')
  if (value === 'nickName') {
    newPlaceholder += intlTrans('sample.搜索达人昵称')
  } else if (value === 'orderNo') {
    newPlaceholder += intlTrans('sample.搜索订单ID')
  } else if (value === 'productId') {
    newPlaceholder += intlTrans('sample.搜索商品ID')
  } else {
    newPlaceholder += intlTrans('sample.搜索达人昵称')
  }
  return setPlaceholder(newPlaceholder)
}
export const searchType = (form: any) => {
  if (form && form.searchValue) {
    let param: any = {}
    switch (form.searchType) {
      case 'nickName':
        param = { ...form, nickname: form.searchValue || '' }
        break
      case 'orderNo':
        param = { ...form, orderNo: form.searchValue || '' }
        break
      case 'productId':
        param = { ...form, productId: form.searchValue || '' }
        break
      default:
        param = { ...form, nickname: form.searchValue || '' }
    }
    delete param?.searchType
    delete param?.searchValue

    return param
  }
  return form
}

export const isNestedColumns = (index: string) => {
  if (index === '0' || index == '2000' || index == '2001') {
    return true
  }
  return false
}

export const statusTag = (status: any) => {
  const style = { margin: '0 8px 8px' }

  switch (status) {
    case 2000:
      return (
        <Tag color="orange" style={style}>
          {intlTrans('sample.待审核')}
        </Tag>
      )
    case 2001:
      return (
        <Tag color="geekblue" style={style}>
          {intlTrans('sample.待发货')}
        </Tag>
      )
    case 3000:
      return (
        <div>
          <div>
            <Tag color="blue" style={style}>
              {intlTrans('sample.已发货')}
            </Tag>
          </div>
          <div>
            <Tag color="magenta" style={style}>
              {intlTrans('sample.待履约')}
            </Tag>
          </div>
        </div>
      )
    case 4000:
      return (
        <Tag color="green" style={style}>
          {intlTrans('sample.已完成')}
        </Tag>
      )
    case -2000:
    case -4000:
      return (
        <Tag color="red" style={style}>
          {intlTrans('sample.审核拒绝')}
        </Tag>
      )
    case -2050:
      return (
        <Tag color="magenta" style={style}>
          {intlTrans('sample.自动拒绝')}
        </Tag>
      )
    // case -2051:
    //   return (
    //     <Tag color="error" style={style}>
    //       {intlTrans('sample.待履约')}
    //     </Tag>
    //   )
    // case -2052:
    //   return (
    //     <Tag color="default" style={style}>
    //       {intlTrans('sample.审核拒绝')}
    //     </Tag>
    //   )
    case -1051:
      return (
        <Tag color="default" style={style}>
          {intlTrans('sample.达人取消')}
        </Tag>
      )
    case -2010:
      return (
        <Tag color="default" style={style}>
          {intlTrans('sample.商家自动同意')}
        </Tag>
      )
    default:
      return (
        <Tag color="default" style={style}>
          没有做标记
        </Tag>
      )
  }
}

export const onBatchReview = (record: any) => {
  const params: any = { orderNos: record }
  return batchReview(params).then((res) => {
    if (res.code == 200) {
      message.success(intlTrans('sample.批量操作成功'))
      return true
    }
    message.error(res.message)
    return false
  })
}
//订单审核
export const onOrderNoReview = (params: any) => {
  return orderNoReview(params).then((res) => {
    if (res.code === 200 && res.result) {
      message.success(intlTrans('common.OperationSuccessful'))
      return true
    }
    message.error(res.message)
    return false
  })
}

// 定义返回对象的接口
interface CustomRenderProps {
  renderFormItem: (schema: any, config: any, form: any, action: any) => React.ReactNode
  formItemProps: { label: string; name?: string | string[] }
  fieldProps: { placeholder: string }
}

// 定义函数参数的接口，建议不要使用 any，给出更精确的类型
interface SearchTypeFromProps {
  setPlaceholder: (value: string) => void
  placeholder: string
}

export const setSearchTypeFrom = ({
  setPlaceholder,
  placeholder
}: SearchTypeFromProps): CustomRenderProps => {
  return {
    renderFormItem: (schema: any, config: any, form: any, action: any) => {
      return (
        <Input.Group compact style={{ display: 'flex' }}>
          <Form.Item name="searchType" noStyle>
            <Select
              dropdownStyle={{ zIndex: 99 }}
              style={{ width: '150px' }}
              placeholder={intlTrans('common.PleaseSelect')}
              onChange={(e) => handleSelectChange(e, setPlaceholder)}
              defaultValue="nickName"
            >
              <Select.Option value="nickName">{intlTrans('sample.达人昵称')}</Select.Option>
              <Select.Option value="orderNo">{intlTrans('sample.订单ID')}</Select.Option>
              <Select.Option value="productId">{intlTrans('sample.商品ID')}</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="searchValue" noStyle>
            <Input
              style={{ width: '300px' }}
              placeholder={placeholder || intlTrans('sample.搜索达人昵称')}
              {...config.fieldProps}
              allowClear
            />
          </Form.Item>
        </Input.Group>
      )
    },
    formItemProps: {
      label: ''
    },
    fieldProps: {
      placeholder: placeholder
    }
  }
}

export const resetData = (formRef: any, setData: any, setExtraParams?: any) => {
  if (setData) {
    setData([])
  }
  if (setExtraParams) {
    setExtraParams({})
  }
  if (formRef) {
    formRef.current?.submit()
  }
}
