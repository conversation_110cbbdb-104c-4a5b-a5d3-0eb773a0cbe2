import { intlTrans } from '@/utils/common'
import { Tabs } from 'antd'
import React, { useEffect, useState } from 'react'
import styles from './index.less'

interface Props {
  handleTab: (key: any) => void
  orderNoNumber: any
}
const SampleTab: React.FC<Props> = ({ handleTab, orderNoNumber }) => {
  useEffect(() => {}, [orderNoNumber])

  const tabArr = [
    {
      label: `${intlTrans('sample.全部')} ${orderNoNumber?.allCount ?? 0}`,
      key: '0'
    },
    {
      label: (
        <div>
          {intlTrans('sample.待审核')} {orderNoNumber?.toBeAuditCount ?? 0}
          {orderNoNumber?.toBeAuditCount ?? 0 ? <span className={styles.red_box} /> : null}
        </div>
      ),
      key: '2000'
    },
    {
      label: `${intlTrans('sample.待发货')} ${orderNoNumber?.toBeDeliveryCount ?? 0}`,
      key: '2001'
    },
    {
      label: `${intlTrans('sample.已发货')} ${orderNoNumber?.toBeReceivedCount ?? 0}`,
      key: '3000'
    },

    {
      label: `${intlTrans('sample.已完成')} ${orderNoNumber?.completedCount ?? 0}`,
      key: '4000'
    },
    {
      label: `${intlTrans('sample.已取消')} ${orderNoNumber?.canceledCount ?? 0}`,
      key: '-2000'
    }
  ]
  return (
    <div className={styles.boxs}>
      <Tabs defaultActiveKey="2000" items={tabArr} onChange={(e) => handleTab(e)} />
    </div>
  )
}

export default SampleTab
