import React, { useEffect, useState } from 'react'
import styles from './index.less'
import { useIntl, useHistory } from 'umi'
import { Modal, Form, Input, Select, message } from 'antd'
import { intlTrans } from '@/utils/common'
import { orderNodeliver, supplementShippingOperation, ttLogistics } from '@/api/sample'
import {
  getShipInfoStorage,
  nationStorage,
  setShipInfoStorage,
  userInfoStorage
} from '@/utils/storage'
export interface IProps {
  history?: any
  shipModalVisable?: boolean
  onConfirmShipModal?: (value: any) => void
  onCloseShipModal?: () => void
  item?: any
}
const ShipModal: React.FC<IProps> = ({
  onCloseShipModal,
  onConfirmShipModal,
  shipModalVisable,
  item
}) => {
  const [formInstance] = Form.useForm()
  const [ttLogisticsList, setTtLogisticsList] = useState([])
  const userInfoId: any = userInfoStorage?.get() ? JSON.parse(userInfoStorage?.get()) : {}
  const nation = nationStorage.get() || ''
  const requestOption = async () => {
    const requsetList: any = await ttLogistics()
    const { code, result } = requsetList
    if (code == 200) {
      setTtLogisticsList(result)
    } else {
      //   message.warning(result.message);
    }
  }
  useEffect(() => {
    console.log('shipModalVisable', userInfoStorage.get())
    if (shipModalVisable) {
      formInstance.setFieldsValue({ ...formInstance.getFieldsValue(), ...item })
      requestOption()

      const userId = userInfoId.id || ''
      const cached = getShipInfoStorage(userId, nation)
      if (cached?.courierCompany) {
        formInstance.setFieldsValue({
          courierCompany: cached.courierCompany
        })
      }
    }
  }, [shipModalVisable])

  const onOrderNodeliver = (params: any) => {
    orderNodeliver(params).then((res) => {
      if (res.code === 200) {
        message.success(intlTrans('common.OperationSuccessful'))
        onConfirmShipModal?.(item)

        setShipInfoStorage(userInfoId.id || '', nation, params.courierCompany)
        formInstance.resetFields()
        onCloseShipModal?.()
      } else {
        message.error(res.message)
      }
      onCloseShipModal?.()
    })
  }
  const onSupplementShippingOperation = (params: any) => {
    supplementShippingOperation(params).then((res) => {
      if (res.code === 200) {
        message.success(intlTrans('common.OperationSuccessful'))
        onConfirmShipModal?.(item)
        formInstance.resetFields()
        onCloseShipModal?.()
      } else {
        message.error(res.message)
      }
      onCloseShipModal?.()
    })
  }
  const orderNODeliverOprate = () => {
    const { courierCompany, trackingNumber } = formInstance.getFieldsValue()
    const params = {
      orderNo: item.orderNo,
      courierCompany: courierCompany,
      trackingNumber: trackingNumber
    }
    if (!item.isLogisticsAdded && item.status == 3000) {
      onSupplementShippingOperation(params)
    } else {
      onOrderNodeliver(params)
    }
  }

  return (
    <>
      {/* 发货弹窗 */}
      <Modal
        title={intlTrans('sample.线下发货')}
        open={shipModalVisable}
        onOk={async () => {
          orderNODeliverOprate()
        }}
        onCancel={() => {
          formInstance.resetFields()
          onCloseShipModal?.()
        }}
      >
        <Form
          form={formInstance}
          name="basic"
          labelCol={{ span: 8 }}
          labelAlign="left"
          wrapperCol={{ span: 16 }}
        >
          {/* 快递公司 */}
          <Form.Item label={intlTrans('sample.物流公司')} name="courierCompany">
            <Select placeholder={intlTrans('common.PleaseSelect')} allowClear>
              {ttLogisticsList.map((item: any, index: any) => {
                return (
                  <Select.Option key={index} value={item}>
                    {item}
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>
          {/* 快递单号 */}
          <Form.Item label={intlTrans('sample.物流单号')} name="trackingNumber">
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}
export default ShipModal
