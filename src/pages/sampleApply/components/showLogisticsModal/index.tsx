import React, { useEffect, useState } from 'react'
import styles from './index.less'
import { Modal, Form, Input, Select, Button, Tag, Steps, Drawer, Spin } from 'antd'
import { intlTrans } from '@/utils/common'
import Copy from '@/components/Copy'
import { getExpressDeliveryInfo } from '@/api/sample'
import { format } from 'prettier'
import { formatTime } from '@/utils/format'
export interface IProps {
  showLogisticsModalVisable?: boolean
  setShowLogisticsModalVisable: any
  item: any
}
const ShowLogisticsModal: React.FC<IProps> = ({
  showLogisticsModalVisable,
  setShowLogisticsModalVisable,
  item
}) => {
  const [logisticsData, setLogisticsData] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const handleLookLogistics = async () => {
    setShowLogisticsModalVisable(true)
    setLoading(true)

    try {
      const res = await getExpressDeliveryInfo({ orderNo: item.orderNo })
      if (res.code === 200) {
        setLogisticsData(res.result)
      } else {
        // 可以根据业务需求处理其他 code 情况
        console.warn('接口返回异常 code:', res.code)
      }
    } catch (error) {
      console.error('获取物流信息失败:', error)
      // 可选：提示用户“获取失败，请稍后再试”
    } finally {
      setLoading(false) // 无论成功失败都关闭 loading
    }
  }

  useEffect(() => {
    if (item && showLogisticsModalVisable) {
      handleLookLogistics()
    }
  }, [showLogisticsModalVisable])

  const handleJumpLogistics = (courierCompany: any) => {
    if (courierCompany) {
      window.open(`https://www.google.com/search?q=${encodeURIComponent(courierCompany)}`)
    }
  }
  return (
    <>
      <Drawer
        footer={null}
        title={intlTrans('sample.查看物流')}
        open={showLogisticsModalVisable}
        placement="right"
        className={styles.drower}
        onClose={() => {
          setShowLogisticsModalVisable(false)
          setLogisticsData({})
        }}>
        <Spin spinning={loading}>
          <div className={styles.title}>{intlTrans('sample.基本信息')} </div>
          {logisticsData.isLogisticsAdded ? (
            <div style={{ marginBottom: '40px' }}>
              <div className={styles.logistic_box}>
                <div>
                  <div>
                    <span className={styles.label}>{intlTrans('sample.物流单号')}: </span>
                    <span className={styles.value}>
                      {logisticsData?.trackingNumberAdd || '---'}
                      {logisticsData?.trackingNumberAdd && (
                        <Copy text={logisticsData?.trackingNumberAdd} />
                      )}
                    </span>
                  </div>
                  <div>
                    <span className={styles.label}>{intlTrans('sample.物流承运商')}: </span>
                    {logisticsData?.courierCompanyAdd && (
                      <span className={styles.value}>
                        {logisticsData?.courierCompanyAdd || '---'}
                        <Button
                          type="link"
                          onClick={() => handleJumpLogistics(logisticsData?.courierCompanyAdd)}>
                          {intlTrans('sample.物流轨迹')}
                        </Button>
                      </span>
                    )}
                  </div>
                  <div>
                    <div className={styles.label}>
                      {intlTrans('sample.提交时间')}: {formatTime(logisticsData?.createTimeAdd)}
                    </div>
                  </div>
                </div>
                <div>
                  <Tag color="blue">{intlTrans('sample.商家补发')}</Tag>
                </div>
              </div>
            </div>
          ) : null}
          <div className={styles.logistic_box}>
            <div>
              <div className={styles.basic_info}>
                <div className={styles.label}>{intlTrans('sample.物流单号')}: </div>
                <div className={styles.value}>
                  {logisticsData?.trackingNumber || '---'}
                  {logisticsData?.trackingNumber && <Copy text={logisticsData?.trackingNumber} />}
                </div>
              </div>
              <div className={styles.basic_info}>
                <div className={styles.label}>{intlTrans('sample.物流承运商')}: </div>
                {logisticsData?.courierCompany && (
                  <span className={styles.value}>
                    {logisticsData?.courierCompany || '---'}
                    <Button
                      className={styles.value}
                      type="link"
                      onClick={() => handleJumpLogistics(logisticsData?.courierCompany)}>
                      {intlTrans('sample.物流轨迹')}
                    </Button>
                  </span>
                )}
              </div>
              <div className={styles.basic_info}>
                <div className={styles.label}>{intlTrans('sample.提交时间')}:</div>
                <div className={styles.value}>{formatTime(logisticsData?.createTime)}</div>
              </div>
            </div>
          </div>

          {/* 跟踪结果 */}
          {logisticsData?.tapDeliverInfo ? (
            <div>
              <div className={styles.track_title}>{intlTrans('sample.跟踪结果')} </div>
              <Steps
                direction="vertical"
                size="small"
                current={logisticsData?.tapDeliverInfo?.logisticsDetails.length - 1}
                items={logisticsData?.tapDeliverInfo?.logisticsDetails.map((item: any) => {
                  return {
                    title: (
                      <div>
                        {item.title} {item.standard}
                      </div>
                    ),
                    description: formatTime(item?.time)
                  }
                })}
              />
            </div>
          ) : null}
          <div className={styles.btn_box}>
            <Button
              style={{ color: '#F59A23' }}
              onClick={() => setShowLogisticsModalVisable(false)}>
              {intlTrans('sample.我知道了')}
            </Button>
          </div>
        </Spin>
      </Drawer>
    </>
  )
}
export default ShowLogisticsModal
