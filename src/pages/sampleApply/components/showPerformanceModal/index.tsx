import React, { useEffect, useRef, useState } from 'react'
import { Modal, Tabs, List, Avatar, Typography, Space, message, Card, Pagination } from 'antd'
import { EyeOutlined, HeartOutlined, MessageOutlined, ScheduleOutlined } from '@ant-design/icons'
import { fulfillmentCounts, ordersFulfillments } from '@/api/sample'
import useTableRequest from '@/hooks/useTableRequest'
import Table from '@/components/Table'
import { intlTrans } from '@/utils/common'
import { formatThTime, formatTime, formatTimeYYMMDD } from '@/utils/format'
import styles from './index.less'
import TitleEllipsis from '@/components/TitleEllipsis'
import ProductImage from '@/components/ProductImage'
const { Text } = Typography
export interface prop {
  performanceVisables: boolean
  setPerformanceVisables: any
  item: any
}

const PerformanceModal = ({ performanceVisables, setPerformanceVisables, item }: prop) => {
  // 当前选中的标签页
  const activeKeyRef = useRef('1')
  const [fulfillmentNumber, setFulfillmentNumber] = useState<any>({})
  const [fulfillmentsList, setFulfillmentsList] = useState<any>([])
  const [loading, setLoading] = useState(false)
  const pageNoRef = useRef(1)
  const [currentPage, setCurrentPage] = useState(1) // 当前页码
  const pageSize = 10 // 每页数量
  const [total, setTotal] = useState(0) // 总条数
  const getPerformanceNum = () => {
    const { orderNo } = item
    fulfillmentCounts(orderNo).then((res) => {
      if (res.code == 200) {
        setFulfillmentNumber(res.result)
      }
    })
  }
  const getFulfillmentsList = async () => {
    setLoading(true)
    const queryParams: any = {
      pageSize: 10,
      pageNo: pageNoRef.current,
      orderNo: item?.orderNo || '',
      type: activeKeyRef.current
    }
    try {
      const res = await ordersFulfillments(queryParams)
      if (res.code === 200) {
        setFulfillmentsList([...res.result.list])
        setTotal(res.result.total)
      }
    } catch (error: any) {
      message.error(error)
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    if (performanceVisables) {
      getPerformanceNum() // 获取履约数量
      getFulfillmentsList() // 获取履约数据
    }
  }, [performanceVisables])

  // Tabs 配置
  const tabItems: any = [
    {
      label: `${intlTrans('sample.视频')} ${fulfillmentNumber?.videoCount || 0}`,
      key: '1',
      children: (
        <List
          loading={loading}
          dataSource={fulfillmentsList}
          className={styles.box}
          pagination={false}
          renderItem={(item: any) => (
            <Card style={{ marginBottom: '10px' }}>
              <List.Item>
                <List.Item.Meta
                  className={styles.item_box}
                  avatar={<ProductImage src={item.coverImg} width={60} height={80} />}
                  // 标题
                  title={<TitleEllipsis content={item.title} line={2} />}
                  // 描述（日期 + 播放量/点赞量）
                  description={
                    <>
                      <div>{formatTimeYYMMDD(item.videoPublishTime)}</div>
                      <Space style={{ marginTop: 8 }}>
                        <span>
                          <EyeOutlined /> {item.viewCount}
                        </span>
                        <span>
                          <HeartOutlined />
                          &nbsp; {item.likeCount}
                        </span>
                        <span>
                          <MessageOutlined />
                          &nbsp; {item.commentCount}
                        </span>
                        <span>
                          <ScheduleOutlined />
                          &nbsp; {item.orderCount}
                        </span>
                      </Space>
                    </>
                  }
                />
                {/* 右侧查看链接 */}
                <a
                  href={`https://www.tiktok.com/player/v1/${item.videoId}?id=${item.videoId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {intlTrans('sample.在TikTok查看视频')}
                </a>
              </List.Item>
            </Card>
          )}
        />
      )
    },
    {
      label: `${intlTrans('sample.直播')} ${fulfillmentNumber?.liveCount || 0}`,
      key: '2',
      children: (
        <Card style={{ marginBottom: '10px' }}>
          <List
            className={styles.box}
            loading={loading}
            pagination={false}
            dataSource={fulfillmentsList}
            renderItem={(item: any) => (
              <List.Item>
                <List.Item.Meta
                  className={styles.item_box}
                  avatar={<ProductImage src={item.coverImg} width={60} height={80} />}
                  // 标题
                  title={<TitleEllipsis content={item.title} line={2} />}
                  description={
                    <>
                      <div>
                        {formatTimeYYMMDD(item.liveStartTime)}~{formatTimeYYMMDD(item.liveEndTime)}
                      </div>
                      <Space style={{ marginTop: 8 }}>
                        <span>
                          <EyeOutlined /> {item.viewCount}
                        </span>
                        <span>
                          <HeartOutlined />
                          &nbsp;
                          {item.likeCount}
                        </span>
                        <span>
                          <ScheduleOutlined />
                          &nbsp;{item.orderCount}
                        </span>
                      </Space>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      )
    }
  ]
  const onTabChange = (key: string) => {
    activeKeyRef.current = key
    pageNoRef.current = 1
    getFulfillmentsList()
  }
  return (
    <Modal
      forceRender
      key={performanceVisables ? 'open' : 'closed'}
      destroyOnClose
      title={intlTrans('sample.履约内容')}
      open={performanceVisables}
      onCancel={() => {
        setPerformanceVisables(false)
        setFulfillmentNumber({})
        setFulfillmentsList([])
        Modal.destroyAll()
        // onCancel()
      }}
      footer={null}
      width={800}
    >
      <div style={{ marginTop: '-10px' }}>
        <Tabs
          activeKey={activeKeyRef.current}
          onChange={(key: string) => onTabChange(key)}
          items={tabItems}
        />
      </div>
      <div className={styles.footer_box}>
        <Pagination
          defaultCurrent={1}
          showSizeChanger={false}
          total={total}
          current={pageNoRef.current}
          pageSize={pageSize}
          onChange={(page) => {
            pageNoRef.current = page
            setCurrentPage(page)
            getFulfillmentsList()
          }}
        />
      </div>
    </Modal>
  )
}

export default PerformanceModal
