import React, { useEffect, useState } from 'react'
import styles from './index.less'
import { <PERSON><PERSON>, Drawer, Modal, Steps } from 'antd'
import { intlTrans } from '@/utils/common'
import Copy from '@/components/Copy'

export const addressArr = (deliveryAddressJSON: any) => [
  { label: intlTrans('sample.收件人'), value: deliveryAddressJSON?.fullName || '--' },
  { label: intlTrans('sample.联系电话'), value: deliveryAddressJSON?.mobile || '--' },
  { label: intlTrans('sample.详细地址'), value: deliveryAddressJSON?.addressDetail || '--' },
  { label: intlTrans('sample.省'), value: deliveryAddressJSON?.provinceName || '--' },
  { label: intlTrans('sample.市'), value: deliveryAddressJSON?.cityName || '--' },
  { label: intlTrans('sample.区'), value: deliveryAddressJSON?.districtName || '--' },
  { label: intlTrans('sample.邮编'), value: deliveryAddressJSON?.postCode || '--' }
]
export interface IProps {
  showReciptInfoModalVisable?: boolean
  setShowReciptInfoModalVisable: (visible: boolean) => void
  deliveryAddress: any
}

const ShowReciptInfoModal: React.FC<IProps> = ({
  showReciptInfoModalVisable,
  setShowReciptInfoModalVisable,
  deliveryAddress
}) => {
  const [deliveryAddressJSON, setDeliveryAddressJson] = useState<any>({})

  useEffect(() => {
    if (typeof deliveryAddress === 'string') {
      try {
        setDeliveryAddressJson(JSON.parse(deliveryAddress))
      } catch (error) {
        console.error('解析 deliveryAddress 失败:', error)
        setDeliveryAddressJson({})
      }
    } else {
      setDeliveryAddressJson(deliveryAddress || {})
    }
  }, [deliveryAddress])

  return (
    <Drawer
      footer={null}
      title={intlTrans('sample.查看收货信息')}
      open={showReciptInfoModalVisable}
      placement="right"
      className={styles.drower}
      onClose={() => setShowReciptInfoModalVisable(false)}
    >
      <div className={styles.title}>
        {intlTrans('sample.基本信息')}{' '}
        {deliveryAddress && (
          <Copy
            text={addressArr(deliveryAddressJSON)
              .map((item) => `${item.label}: ${item.value}`)
              .join('\n')}
          />
        )}
      </div>
      {deliveryAddress && (
        <div className={styles.address_info}>
          {addressArr(deliveryAddressJSON).map((item, index) => (
            <div key={index} className={styles.address_box}>
              <div className={styles.label}>{item.label}:</div>
              <div className={styles.value}>{item.value}</div>
            </div>
          ))}
        </div>
      )}
    </Drawer>
  )
}

export default ShowReciptInfoModal
