.boxs {
  padding: 24px 0;
  background-color: #fff;
}

.toolBarRender_box {
  display: flex;
  align-items: left;
  justify-content: left;
  width: 100%;
}

.filter_btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 60px;
  margin-left: 40px;
  padding: 0 8px;
  background-color: #eee6e6;
  border-radius: 4px;
  cursor: pointer;
}

.tips_box {
  padding: 0 24px;
}

.product_box {
  display: flex;
  min-width: 200px;
  margin-top: 10px;
}

.product_img {
  width: 60px;
  height: 60px;
  margin-right: 8px;
}

.product_content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin-left: 8px;
}

.product_content_title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: gray;
}

.filterNumber_box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  margin-left: 6px;
  padding: 4px;
  color: #fff;
  background-color: #000;
  border-radius: 50%;
}

.good_anchor_title {
  margin-left: 8px;
  color: red;
}

.good_anchor {
  width: 20;
  height: 20px;
}

.reason_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.recommend_reason {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reason {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // width: 220px;
}

.tooltip {
  margin-left: 8px;
}

.live_video {
  margin-left: 8px;
}

.table_title_container {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.table_title_agree_container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.influencer_title_container {
  display: flex;
  align-items: center;
}

.influencer_title_checkbox {
  display: flex;
  align-items: center;
  margin-right: 36px;

  .label {
    margin-left: 8px;
    color: #999;
    font-weight: normal;
  }
}

.sub_table_container {
  padding: 0 24px;

  :global {
    .ant-table-selection-column {
      padding-left: 24px !important;
    }

    .ant-table-thead > tr > th {
      background-color: #fbfbfb !important;
    }

    .ant-table-row {
      background-color: #fbfbfb;
    }

    .ant-checkbox {
      right: 12px !important;
    }
  }
}

.table_container {
  :global {
    .ant-table {
      border: 2px solid #f0f0f0;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.product_content_title_box {
  display: flex;
  align-items: center;
}
