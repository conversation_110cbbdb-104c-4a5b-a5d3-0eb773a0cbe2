import React, { Children, useEffect, useRef, useState } from 'react'
import CustomizeTable from '@/components/Table'
import type { FormInstance } from 'antd'
import { Image, Tag, Button, Select, Modal, Alert, Tooltip, Checkbox, Table, Space } from 'antd'
import withPageTitle from '@/HOCs/withPageTitle'
import { intlTrans } from '@/utils/common'
import SampleTab from './components/sampleTab'
import styles from './index.less'
import {
  handleSelectChange,
  isNestedColumns,
  onBatchReview,
  onOrderNoReview,
  resetData,
  searchType,
  setSearchTypeFrom,
  statusTag
} from './components/sampleComPart'
import {
  CheckCircleTwoTone,
  DownOutlined,
  ExclamationCircleFilled,
  FilterOutlined,
  QuestionCircleOutlined,
  UpOutlined
} from '@ant-design/icons'
import RefuseModal from './components/refuseModal'
import DrawerModal from './components/drawerModal'
import { DownLoadBtn } from './components/downloadBtn'
import ShipModal from './components/shipModal'
import ShowReciptInfoModal, { addressArr } from './components/showReciptInfoModal'
import ShowLogisticsModal from './components/showLogisticsModal'
import PerformanceModal from './components/showPerformanceModal'
import { anchorsOrders, ordersItems, ordersStats, suppliersShops } from '@/api/sample'
import useTableRequest from '@/hooks/useTableRequest'
import type { ActionType } from '@ant-design/pro-components'
import ProductImage from '@/components/ProductImage'
import ErrorImage from '@/components/ErrorImage'
import TitleEllipsis from '@/components/TitleEllipsis'
import Copy from '@/components/Copy'
import { formatHours, formatSales, formatUnit } from '@/utils/format'
import { cancelAllRequests, cancelOrderItemsRequests, cancelRequest } from '@/services/http'
import useShopAuthLock from '@/hooks/useShopAuthLock'
import useSampleReview from '@/hooks/useSampleReview'
import CustomizeAvatar from '@/components/Avatar'
import icon_good_anchor from '@/assets/images/icons/icon_good_anchor.png'
import { copy } from '@/utils/copy'

const wrapperPageSize = 10
const subPageSize = 10

const scrollToRecord = (id: string) => {
  const row = document.querySelector(`#sample-wrap-list-${id}`)
  if (row) {
    row.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const getSelectedOrderNo = (data: any) => {
  const orderNoList: any = []
  data.forEach((record: any) => {
    if (record.subList && record.subList.list && record.subList.list.length > 0) {
      record.subList.list.forEach((item: any) => {
        if (item.checked) {
          orderNoList.push(item.orderNo)
        }
      })
    }
  })
  return orderNoList
}

const existSubList = (data: any) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].subList && data[i].subList.list && data[i].subList.list.length > 0) {
      return true
    }
  }
  return false
}

const allChecked = (data: any) => {
  if (data.length === 0) {
    return false
  }
  for (let i = 0; i < data.length; i++) {
    if (data[i].subList && data[i].subList.list && data[i].subList.list.length > 0) {
      for (let j = 0; j < data[i].subList.list.length; j++) {
        if (!data[i].subList.list[j].checked) {
          return false
        }
      }
    }
  }
  return true
}

const subListAllChecked = (subList: any) => {
  if (!subList) {
    return false
  }
  if (subList && subList.list && subList.list.length === 0) {
    return false
  }
  if (subList && subList.list && subList.list.length > 0) {
    for (let i = 0; i < subList.list.length; i++) {
      if (!subList.list[i].checked) {
        return false
      }
    }
  }
  return true
}

const SampleApply = () => {
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  const [placeholder, setPlaceholder] = useState(intlTrans('sample.搜索达人昵称'))
  const [reviewLoading, setReviewLoading] = useState<boolean>(false)
  const [isRefuseModalOpen, setIsRefuseModalOpen] = useState<boolean>(false)
  const [currentItem, setCurrentItem] = useState<any>()
  const [isDrawerModalOpen, setIsFilterDrawerOpen] = useState<boolean>(false)
  const [filterNumber, setFilterNumber] = useState<number>(0)
  const formRef = useRef<FormInstance>()
  const [shipModalVisable, setShipModalVisable] = useState<boolean>(false)
  const [showReciptInfoModalVisible, setShowReciptInfoModalVisible] = useState<boolean>(false)
  const [showLogisticsModalVisible, setShowLogisticsModalVisible] = useState<boolean>(false)
  const [performanceVisables, setPerformanceVisables] = useState<boolean>(false)
  const [tabIndex, setTabIndex] = useState('2000')
  const tabIndexRef = useRef('2000')
  const actionRef = useRef<ActionType>()
  const [data, setData] = useState<any[]>([])
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([])
  const [alertVisible, setAlertVisible] = useState({
    alert1: true,
    alert2: true
  })
  const [orderNoNumber, setOrderNoNumber] = useState<any>()
  const [extraParams, setExtraParams] = useState<any>({})
  const [supplierShops, setSupplierShops] = useState<any>([])
  const [childDataLoading, setChildDataLoading] = useState(false)

  useShopAuthLock()
  useSampleReview()

  const onOrderStatus = async () => {
    const params: any = {
      status: tabIndexRef.current,
      ...extraParams,
      ...formRef.current?.getFieldsValue()
    }
    const res = await ordersStats({ ...(await searchType(params)) })
    if (res.code === 200) {
      setOrderNoNumber(res.result)
    }
    return null
  }

  const handleShop = async () => {
    const res = await suppliersShops()
    if (res.code === 200) {
      const value = res.result.map((item: any) => {
        return {
          label: item.shopName,
          value: item.shopCode
        }
      })
      setSupplierShops(value)
    }
  }

  useEffect(() => {
    handleShop()
  }, [])

  const requestSubList = async (memberId: string, pageNo: number = 1) => {
    return new Promise(async (resolve, reject) => {
      const params = {
        memberId: memberId,
        unprocessedHours: extraParams?.unprocessedHours || '',
        status: tabIndex,
        ...formRef.current?.getFieldsValue(),
        pageNo: pageNo,
        pageSize: subPageSize
      }
      try {
        const res = await ordersItems({ ...(await searchType(params)) })
        if (res.code === 200 && res.result) {
          resolve({
            list: res.result.list || [],
            total: res.result.total || 0,
            current: pageNo
          })
        } else {
          resolve({
            list: [],
            total: 0,
            current: 1
          })
        }
      } catch (error) {
        resolve({
          list: [],
          total: 0,
          current: 1
        })
      }
    })
  }

  const requestSubListAndUpdateData = async (
    accountId: string,
    pageNo: number = -1,
    scroll: boolean = false
  ) => {
    onOrderStatus()
    try {
      const tempPageNo =
        pageNo === -1 ? data.find((item) => item.memberId == accountId).subList.current : pageNo
      setTableLoading(true)
      const subList: any = await requestSubList(accountId, tempPageNo)
      setTableLoading(false)
      const { list, total } = subList
      // 如果通过并且当前页面是最后一页， 请求上一页的数据
      if (list.length === 0 && total !== 0) {
        requestSubListAndUpdateData(accountId, tempPageNo - 1)
        return
      }
      if (subList) {
        data.find((item) => item.memberId == accountId).subList = subList
        data.find((item) => item.memberId == accountId).orderCount = subList.total
        newSetData(data)
        if (scroll) {
          setTimeout(() => {
            scrollToRecord(accountId)
          }, 100)
        }
      }
    } catch (error) {
      setTableLoading(false)
    }
  }

  const onRequest = useTableRequest(async (params) => {
    onOrderStatus()

    try {
      const queryParams = {
        status: tabIndex,
        ...params,
        ...extraParams
      }
      setTableLoading(true)
      const res = await anchorsOrders({ ...(await searchType(queryParams)) })
      if (res.code === 200 && res.result) {
        const list = res.result.list || []
        const total = res.result.total || 0
        const current = res.result.current || 1
        // 如果通过并且当前页面是最后一页， 重新请求第一页的数据
        if (list.length === 0 && total !== 0) {
          setTableLoading(false)
          setTimeout(() => {
            formRef.current?.submit()
          }, 500)
          return Promise.resolve(res)
        }
        if (tabIndex === '0' || tabIndex === '2000' || tabIndex === '2001') {
          const requestSubListPromises = res.result.list.map((item: any) =>
            requestSubList(item.memberId)
          )
          const results = await Promise.all(requestSubListPromises)
          list.forEach((item: any, index: number) => {
            if (results[index]) {
              item.subList = results[index]
            }
          })
          setExpandedRowKeys(list.map((item: any) => item.memberId))
        }
        setTableLoading(false)
        setData(list)
      } else {
        setTableLoading(false)
        setData([])
      }
      return Promise.resolve(res)
    } catch (error) {
      setTableLoading(false)
      return Promise.reject(error)
    }
  })

  const handleReviewAgree = async (item: any) => {
    setReviewLoading(true)
    Modal.confirm({
      icon: <ExclamationCircleFilled />,
      content: (
        <div>
          <span style={{ color: '#fe2c45' }}>
            {' '}
            {intlTrans('sample.同意后样品申请将进入待发货')}
          </span>
          <span>{intlTrans('sample.你还要继续吗')}</span>
        </div>
      ),
      okText: intlTrans('sample.继续'),
      cancelText: intlTrans('sample.取消'),
      onOk: async () => {
        const param = {
          action: 1,
          orderNo: item.orderNo
        }

        const okText: any = await onOrderNoReview(param)
        if (okText) {
          requestSubListAndUpdateData(item.accountId)
        }
      },

      onCancel() {
        console.log('Cancel')
      }
    })
  }
  const disAgreeReason = (item: any) => {
    if (!item.agreeStatus && item.disAgreeReasonCode) {
      if (item.disAgreeReasonCode == 2) {
        return (
          <div>
            {intlTrans('sample.活动样品额度不足')}{' '}
            <Button
              type="link"
              onClick={() => {
                window.open(item.campaignLink)
              }}>
              {intlTrans('sample.补充额度')}
            </Button>
          </div>
        )
      } else if (item.disAgreeReasonCode == 1) {
        return intlTrans('sample.商品或SKU缺货请前TikTokShop补库存')
      }
    }
  }
  const oprationStauts = (status: any, item: any) => {
    switch (status) {
      case 2000:
        return (
          <>
            <Tooltip title={disAgreeReason(item)}>
              <Button
                type={!item.agreeStatus ? 'text' : 'link'}
                disabled={!item.agreeStatus}
                onClick={() => {
                  setCurrentItem(item)
                  handleReviewAgree(item)
                }}>
                {intlTrans('sample.同意')}
              </Button>
            </Tooltip>
            <Button
              type="link"
              style={{ color: 'red' }}
              onClick={() => {
                setCurrentItem(item)
                setIsRefuseModalOpen(true)
              }}>
              {intlTrans('sample.拒绝')}
            </Button>
          </>
        )
      case 2001:
        return (
          <>
            <Button
              type="link"
              style={{ color: 'blue' }}
              onClick={() => {
                if (item.source == 3) {
                  window.open(item.deliveryLink)
                  return
                }
                setCurrentItem(item)
                setShipModalVisable(true)
              }}>
              {item.source == 3 ? (
                <div>{intlTrans('sample.tiktokShop发货')}</div>
              ) : (
                intlTrans('sample.发货')
              )}
            </Button>
            {item.source == 3 && <Copy text={item.deliveryLink} />}
            {item.source != 3 && (
              <Button
                type="link"
                style={{ color: 'blue' }}
                onClick={() => {
                  setCurrentItem(item)
                  setShowReciptInfoModalVisible(true)
                }}>
                {intlTrans('sample.查看收货信息')}
              </Button>
            )}
            {/* 一键复制收货信息 */}
            {item.source != 3 && (
              <Button
                type="link"
                style={{ color: 'blue' }}
                onClick={() => {
                  copy(
                    addressArr(JSON.parse(item?.deliveryAddress || {}))
                      .map((item) => `${item.label}: ${item.value}`)
                      .join('\n')
                  )
                }}>
                {intlTrans('sample.一键复制收货信息')}
              </Button>
            )}
          </>
        )
      case 3000:
        return (
          <>
            <Button
              type="link"
              style={{ color: 'blue' }}
              onClick={() => {
                setCurrentItem(item)
                setShowLogisticsModalVisible(true)
              }}>
              {intlTrans('sample.查看物流单号')}
            </Button>
            {item.status === 3000 && !item.isLogisticsAdded && item.source != 3 && (
              <Button
                type="text"
                style={{ color: 'blue' }}
                onClick={() => {
                  setCurrentItem(item)
                  setShipModalVisable(true)
                }}>
                {intlTrans('sample.增补发货')}
              </Button>
            )}
          </>
        )
      case 4000:
        return (
          <>
            <Button
              type="text"
              style={{ color: 'blue' }}
              onClick={() => {
                setCurrentItem(item)
                setPerformanceVisables(true)
              }}>
              {intlTrans('sample.查看履约内容')}
            </Button>

            <Button
              type="text"
              style={{ color: 'blue' }}
              onClick={() => {
                setCurrentItem(item)
                setShowLogisticsModalVisible(true)
              }}>
              {intlTrans('sample.查看物流单号')}
            </Button>
          </>
        )
      case -2000:
        return intlTrans('sample.商家拒绝')
      case -2050:
        return intlTrans('sample.商家自动拒绝')
      default:
        return '-'
    }
  }

  const handleAllSelectChange = (e: any) => {
    data.forEach((item: any) => {
      if (item.subList && item.subList.list && item.subList.list.length > 0) {
        item.subList.list.forEach((subItem: any) => {
          subItem.checked = e.target.checked
        })
      }
    })
    newSetData(data)
  }

  const handleSubListAllCheckedChange = (e: any, subList: any) => {
    if (subList && subList.list && subList.list.length > 0) {
      subList.list.forEach((subItem: any) => {
        subItem.checked = e.target.checked
      })
    }
    newSetData(data)
  }

  const newSetData = (data: any) => {
    setData([...data])
  }

  const columns1 = [
    {
      title: (
        <div className={styles.influencer_title_container}>
          {tabIndex === '2000' && (
            <div className={styles.influencer_title_checkbox}>
              <Checkbox
                disabled={!existSubList(data)}
                checked={allChecked(data)}
                onChange={handleAllSelectChange}
              />
              <span className={styles.label}>{intlTrans('locale.全选本页')}</span>
            </div>
          )}
          {intlTrans('sample.达人')}
        </div>
      ),
      dataIndex: 'influencer',
      key: 'influencer',
      colSize: 2,
      width: 300,
      render: (_: any, record: any) => (
        <Space direction="horizontal">
          {tabIndex === '2000' && (
            <div style={{ marginLeft: 8 }}>
              <Checkbox
                checked={subListAllChecked(record.subList)}
                onChange={(e) => handleSubListAllCheckedChange(e, record.subList)}
              />
            </div>
          )}
          <div style={{ position: 'relative' }}>
            <div
              style={{ position: 'absolute', top: -72 }}
              id={`sample-wrap-list-${record.memberId}`}
            />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <CustomizeAvatar avatar={record.avatar} />
              <div className={styles.box_right}>
                <div style={{ marginLeft: 8 }}>
                  <div style={{ display: 'flex' }}>
                    <div
                      style={{ color: '#009995', cursor: 'pointer' }}
                      onClick={() => {
                        window.open(record.tiktokUrl)
                      }}>
                      @{record.displayName}
                    </div>
                    <Copy text={record.displayName} />
                    {record.goodAnchor && tabIndex == '2000' ? (
                      <div className={styles.good_anchor_title}>
                        <Image
                          src={icon_good_anchor}
                          preview={false}
                          className={styles.good_anchor}
                        />
                        {intlTrans('sample.优质带货达人')}
                      </div>
                    ) : null}
                  </div>
                  <div style={{ fontWeight: 'bold', marginTop: '5px' }}>{record.nickname}</div>
                </div>
                {tabIndex == '2000' && (
                  <div className={styles.live_video}>
                    <Tag color="#f50" style={{ marginRight: '5px', marginBottom: '5px' }}>
                      {intlTrans('sample.视频')}
                    </Tag>
                    {record.hasLive && (
                      <Tag color="#108ee9" style={{ marginRight: '5px', marginBottom: '5px' }}>
                        {intlTrans('sample.直播')}
                      </Tag>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Space>
      ),
      ...setSearchTypeFrom({ setPlaceholder, placeholder })
    },
    {
      title: intlTrans('sample.粉丝数'),
      dataIndex: 'followerCount',
      key: 'followerCount',
      align: 'center',
      width: 100,
      colSize: 1.5,
      hideInSearch: tabIndexRef.current == '2001' ? false : true,
      render: (text: any) => <span>{formatUnit(text, '', true)}</span>,
      renderFormItem: (schema: any, config: any, form: any, action: any) => {
        return <Select {...config.fieldProps} allowClear dropdownStyle={{ zIndex: 99 }} />
      },
      formItemProps: {
        label: intlTrans('sample.发货方式'),
        name: 'deliveryType'
      },
      fieldProps: {
        placeholder: intlTrans('common.PleaseSelect'),
        options: [
          { label: intlTrans('sample.线上'), value: 2 },
          { label: intlTrans('sample.线下'), value: 1 }
        ]
      }
    },
    {
      title: intlTrans('sample.销量'),
      dataIndex: 'unitsSold',
      key: 'unitsSold',
      align: 'center',
      width: 100,

      render: (text: any) => <span>{text || text == 0 ? formatSales(text) : '/'}</span>,
      renderFormItem: (schema: any, config: any, form: any, action: any) => {
        return (
          <Select
            {...config.fieldProps}
            style={{ width: '300px' }}
            allowClear
            dropdownStyle={{ zIndex: 99 }}
          />
        )
      },
      formItemProps: {
        label: intlTrans('sample.店铺'),
        name: 'shopCode'
      },
      fieldProps: {
        placeholder: intlTrans('common.PleaseSelect'),
        options: supplierShops
      }
    },
    {
      title: intlTrans('sample.历史履约率'),
      dataIndex: 'fulfillmentRate',
      key: 'fulfillmentRate',
      align: 'center',
      width: 150,
      hideInSearch: true,
      render: (text: any) => <span>{(text || text == 0) && text != '-1' ? text + '%' : '/'}</span>
    },
    //推荐理由
    {
      title: (
        <div className={styles.recommend_reason}>
          {intlTrans('sample.推荐理由')}
          <Tooltip
            title={intlTrans('sample.根据您设置的达人申样门槛推荐')}
            className={styles.tooltip}>
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      ),
      dataIndex: 'fansReason',
      key: 'fansReason',
      align: 'center',
      width: 250,
      hideInSearch: true,
      render: (text: any, record: any) => (
        <>
          {record.fansReason &&
          record.soldReason &&
          !record.fansReason.good &&
          !record.soldReason.good ? (
            '/'
          ) : (
            <div className={styles.reason_box}>
              <div>
                {record.fansReason && record.fansReason?.good ? (
                  <div className={styles.reason}>
                    <CheckCircleTwoTone twoToneColor="#52c41a" /> &nbsp;
                    <div style={{ textAlign: 'left' }}>
                      {' '}
                      {intlTrans('sample.粉丝数达标')} (≥
                      {formatUnit(record.fansReason?.value)})
                    </div>
                  </div>
                ) : null}
                {record.soldReason && record.soldReason?.good ? (
                  <div className={styles.reason}>
                    <CheckCircleTwoTone twoToneColor="#52c41a" /> &nbsp;
                    <div style={{ textAlign: 'left' }}>
                      {' '}
                      {intlTrans('sample.月销售件数达标')} (≥
                      {record.soldReason?.value}
                      {intlTrans('account.件')})
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          )}
        </>
      )
    },
    {
      title: intlTrans('sample.申请数量'),
      dataIndex: 'orderCount',
      hideInSearch: true,
      align: 'center',
      width: 100,
      key: 'orderCount',
      render: (text: any, record: any, index: any, action: any) => {
        return (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <div>{text}</div>
            <div style={{ marginLeft: 8 }}>{record.expandIcon}</div>
          </div>
        )
      }
    }
  ]
  const columns2: any = [
    {
      title: intlTrans('sample.达人'),
      dataIndex: 'user',
      key: 'user',
      width: 280,
      colSize: 2,
      align: 'center',
      render: (text: any, record: any) => (
        <>
          <span style={{ color: 'gray' }}>
            {intlTrans('sample.订单ID')}:{record.orderNo}
            <Copy text={record.orderNo} />
            {record.source && (
              <Tag color={record.source == 3 ? 'green' : 'red'} style={{ marginLeft: '8px' }}>
                {record.source == 3 ? intlTrans('sample.线上') : intlTrans('sample.线下')}
              </Tag>
            )}
          </span>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CustomizeAvatar avatar={record.avatar} />
            <div className={styles.box_right}>
              <div style={{ marginLeft: 8 }}>
                <a href={record.tiktokUrl} target="_blank" rel="noreferrer">
                  @{record.displayName}
                </a>
                <div style={{ fontWeight: 'bold' }}>{record.nickname}</div>
              </div>
            </div>
          </div>
        </>
      ),

      ...setSearchTypeFrom({ setPlaceholder, placeholder })
    },
    {
      title: intlTrans('sample.粉丝数'),
      hideInSearch: true,
      align: 'center',
      dataIndex: 'followerCount',
      key: 'followerCount',
      render: (text: any) => <span>{formatUnit(text, '', true)}</span>
    },
    {
      title: intlTrans('sample.销量'),
      dataIndex: 'unitsSold',
      key: 'unitsSold',
      align: 'center',
      colSize: 1.5,
      render: (text: any) => <span>{text || text == 0 ? formatSales(text) : '/'}</span>,
      renderFormItem: (schema: any, config: any, form: any, action: any) => {
        return <Select {...config.fieldProps} allowClear dropdownStyle={{ zIndex: 99 }} />
      },
      formItemProps: {
        label: intlTrans('sample.店铺'),
        name: 'shopCode'
      },
      fieldProps: {
        placeholder: intlTrans('common.PleaseSelect'),
        options: supplierShops
      }
    },
    {
      title: intlTrans('sample.商品'),
      dataIndex: 'itemUrl',
      hideInSearch: true,
      align: 'center',
      width: 250,
      key: 'itemUrl',
      render: (text: any, item: any) => (
        <div className={styles.product_box}>
          {text && text != '-' ? (
            <ProductImage src={text} width={40} height={40} />
          ) : (
            <ErrorImage />
          )}
          <div className={styles.product_content}>
            <TitleEllipsis content={item.itemTitle} />
            <div style={{ fontSize: 12, color: '#888' }}>ID: {item.itemId}</div>
          </div>
        </div>
      )
    },
    {
      title: intlTrans('sample.SKU'),
      dataIndex: 'specifications',
      hideInSearch: true,
      align: 'center',
      key: 'specifications',
      render: (text: any, item: any) => (
        <>
          <div>{text}</div>
          {item.count ? <div style={{ color: '#333' }}>x{item.count}</div> : null}
        </>
      )
    },
    {
      title: intlTrans('sample.佣金率'),
      align: 'center',
      dataIndex: 'commissionFeeRate',
      hideInSearch: true,
      key: 'commissionFeeRate',
      render: (text: any) => <span>{text || text === 0 ? `${text}%` : '0%'}</span>
    },
    {
      title: intlTrans('sample.状态'),
      dataIndex: 'status',
      hideInSearch: true,
      align: 'center',
      width: 200,
      key: 'status',
      render: (status: string[], item: any) => (
        <div>
          <div>{statusTag(status)}</div>

          {item.status == '3000' && item.isLogisticsAdded && (
            <div>
              {' '}
              <Tag color="blue" style={{ marginRight: '5px', marginBottom: '5px' }}>
                {intlTrans('sample.已增补发货')}
              </Tag>
            </div>
          )}
        </div>
      )
    },
    tabIndexRef.current == '-2000' || tabIndexRef.current == '-4000'
      ? {
          title: intlTrans('sample.拒绝理由'),
          dataIndex: 'action',
          key: 'action',
          width: 300,
          align: 'center',
          hideInSearch: true,
          render: (action: string, item: any) => (
            <>
              <TitleEllipsis content={item.reason} />
            </>
          )
        }
      : {
          title: intlTrans('sample.操作'),
          dataIndex: 'status',
          aligin: 'status',
          key: 'status',
          align: 'center',
          width: 300,
          hideInSearch: true,

          render: (status: string, item: any) => <>{oprationStauts(status, item)}</>
        }
  ]

  const handleOnExpand = async (expanded: boolean, record: any) => {
    const memberId = record.memberId ? record.memberId : record.accountId
    if (expanded) {
      setExpandedRowKeys((prev: any) => [...prev, memberId])
    } else {
      setExpandedRowKeys((prev: any) => prev.filter((id: any) => id !== memberId))
    }
  }

  const expandedRowRender = (record: any) => {
    const orderColumns: any = [
      {
        title: intlTrans('sample.商品'),
        dataIndex: 'itemUrl',
        key: 'itemUrl',
        align: 'center',
        width: 400,
        render: (text: any, product: any) => (
          <>
            <div>
              <div className={styles.product_content_title}>
                {intlTrans('sample.订单ID')}:{product.orderNo}
                <Copy text={product.orderNo} />
                {product.status == 2001 ? (
                  <Tag color={product.source == 3 ? 'green' : 'red'} style={{ marginLeft: '4px' }}>
                    {product.source == 3 ? intlTrans('sample.线上') : intlTrans('sample.线下')}
                  </Tag>
                ) : null}
              </div>
            </div>
            <div className={styles.product_box}>
              {text && text != '-' ? (
                <ProductImage src={text} width={40} height={40} />
              ) : (
                <ErrorImage />
              )}
              <div className={styles.product_content}>
                <TitleEllipsis content={product.itemTitle} />
                <div style={{ fontSize: 12, color: '#888' }}>
                  ID: {product.itemId} <Copy text={product.itemId} IconColor="#ccc" />
                </div>
              </div>
            </div>
          </>
        )
      },
      {
        title: intlTrans('sample.SKU'),
        align: 'center',
        dataIndex: 'specifications',
        key: 'specifications',
        width: 200,
        render: (text: any, item: any) => (
          <>
            <div>{text}</div>
            {item.count ? <div style={{ color: '#333' }}>x{item.count}</div> : null}
          </>
        )
      },
      {
        title: intlTrans('sample.佣金率'),
        dataIndex: 'commissionFeeRate',
        align: 'center',
        key: 'commissionFeeRate',
        width: 100,
        render: (text: any) => <span>{text || text === 0 ? `${text}%` : '0%'}</span>
      },
      {
        title: intlTrans('sample.状态'),
        dataIndex: 'status',
        hideInSearch: true,
        align: 'center',
        key: 'status',
        width: 200,
        render: (status: string[], item: any) => (
          <div>
            <div>{statusTag(status)}</div>
            {item.isLogisticsAdded && (
              <Tag color="blue" style={{ marginRight: '5px', marginBottom: '5px' }}>
                {intlTrans('sample.已增补发货')}
              </Tag>
            )}
            {(item.status == '-2000' || item.status == '-4000') && (
              <div style={{ marginTop: '5px', marginBottom: '5px' }}>
                {' '}
                <Tooltip title={item.reason}>
                  <TitleEllipsis content={item.reason} />
                </Tooltip>
              </div>
            )}
          </div>
        )
      },
      {
        title: intlTrans('sample.未处理时间'),
        dataIndex: 'unprocessedDuration',
        align: 'center',
        key: 'unprocessedDuration',
        width: 150,
        render: (text: any) => <span>{text ? formatHours(text) ?? '0' : '/'}</span>
      },
      {
        title: intlTrans('sample.操作'),
        dataIndex: 'status',
        align: 'center',
        key: 'status',
        width: 300,
        render: (item: any, record: any) => <div>{oprationStauts(item, record)}</div>
      }
    ]

    const subList = record.subList || {}
    const { list, current, total } = subList

    return (
      <div className={styles.sub_table_container}>
        <Table
          id="orderNo"
          className={styles.table_container}
          loading={childDataLoading}
          columns={orderColumns}
          dataSource={list || []}
          rowKey="orderNo"
          scroll={{ y: 400 }}
          rowSelection={
            tabIndex == '2000'
              ? {
                  selectedRowKeys: list
                    ? list.filter((item: any) => item.checked).map((item: any) => item.orderNo)
                    : [],
                  onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
                    list.forEach((item: any) => {
                      item.checked = selectedRowKeys.includes(item.orderNo)
                    })
                    newSetData(data)
                  },
                  getCheckboxProps: (record: any) => ({
                    disabled: record.agreeStatus
                  }),
                  hideSelectAll: true
                }
              : undefined
          }
          pagination={{
            size: 'small',
            current: current || 1,
            pageSize: subPageSize,
            total: total || 0,
            showSizeChanger: false,
            onChange: (page: number) => requestSubListAndUpdateData(record.memberId, page, true),
            showTotal(total, range) {
              return `${intlTrans('locale.total')} ${total}`
            }
          }}
        />
      </div>
    )
  }

  const handleSelectedModal = (selectedRowKeys: any) => {
    Modal.confirm({
      title: intlTrans('sample.批量同意20条').replace('{{word}}', selectedRowKeys.length),
      icon: <ExclamationCircleFilled />,
      content: (
        <div>
          <span style={{ color: '#fe2c45' }}>
            {' '}
            {intlTrans('sample.同意后样品申请将进入待发货')}
          </span>
          <span>{intlTrans('sample.你还要继续吗')}</span>
        </div>
      ),
      okText: intlTrans('sample.继续'),
      cancelText: intlTrans('sample.取消'),
      onOk: async () => {
        const okText: any = await onBatchReview(selectedRowKeys)
        if (okText) {
          actionRef.current?.reload()
        }
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }

  const handleRefuseOk = () => {
    setIsRefuseModalOpen(false)
    requestSubListAndUpdateData(currentItem.accountId)
  }
  const onCheckTimebox = (e: any) => {
    const updatedExtraParams = { ...extraParams }
    if (!e.target.checked && updatedExtraParams.unprocessedHours) {
      delete updatedExtraParams.unprocessedHours
      setExtraParams(updatedExtraParams)
    } else {
      const params = {
        ...updatedExtraParams,
        unprocessedHours: tabIndexRef.current === '2000' ? '24' : '72'
      }
      setExtraParams(params)
    }

    formRef.current?.submit()
  }

  const handleFilterOk = (value: any, num: any) => {
    setFilterNumber(num)
    setIsFilterDrawerOpen(false)
    setExtraParams(value)
    setTimeout(() => {
      formRef.current?.submit()
    })
  }

  const handleTab = (e: any) => {
    cancelOrderItemsRequests()
    cancelRequest('anchorsOrdersKey')
    setTabIndex(e)
    tabIndexRef.current = e
    setFilterNumber(0)
    setIsFilterDrawerOpen(false)
    resetData(formRef, setData, setExtraParams)
  }

  return (
    <div className={styles.boxs}>
      <SampleTab handleTab={handleTab} orderNoNumber={orderNoNumber} />
      <div className={styles.tips_box}>
        {tabIndex == '2000' && alertVisible.alert1 && orderNoNumber?.toBeAuditCount ? (
          <Alert
            message={
              <>
                <span>
                  {intlTrans('sample.样品申请等待审核tips').replace(
                    '{{word}}',
                    orderNoNumber?.toBeAuditCount || 0
                  )}
                </span>
                &nbsp;&nbsp;
                <span style={{ color: 'red' }}>{intlTrans('sample.超时将自动取消样品申请')}</span>
              </>
            }
            type="error"
            closable
          />
        ) : null}
        {tabIndex == '2001' && alertVisible.alert2 && orderNoNumber?.toBeDeliveryCount ? (
          <Alert
            message={intlTrans('sample.品申请等待发货tips').replace(
              '{{word}}',
              orderNoNumber?.toBeDeliveryCount || 0
            )}
            type="error"
            closable
          />
        ) : null}
      </div>
      <CustomizeTable
        loading={tableLoading}
        id="memberId"
        className={styles.sub_table_container}
        formRef={formRef}
        actionRef={actionRef}
        request={onRequest}
        toolbar={{
          title: (
            <div className={styles.table_title_container}>
              {tabIndex == '2000' && (
                <div className={styles.table_title_agree_container}>
                  <div style={{ fontSize: 14, marginRight: 14 }}>
                    {intlTrans('sample.已选择单').replace(
                      '{{word}}',
                      getSelectedOrderNo(data).length
                    )}
                  </div>
                  <Button
                    disabled={getSelectedOrderNo(data).length === 0}
                    type="primary"
                    onClick={() => {
                      handleSelectedModal(getSelectedOrderNo(data))
                    }}>
                    {intlTrans('sample.同意')}
                  </Button>
                </div>
              )}
              {tabIndex == '2000' || tabIndex == '2001' ? (
                <div key="id" className={styles.toolBarRender_box}>
                  <Checkbox onChange={onCheckTimebox} checked={!!extraParams.unprocessedHours}>
                    {tabIndex == '2000'
                      ? intlTrans('sample.超24小时未处理')
                      : tabIndex == '2001'
                      ? intlTrans('sample.超3天未处理')
                      : null}
                  </Checkbox>
                </div>
              ) : null}
            </div>
          )
        }}
        options={{
          reload: false,
          setting: false,
          density: false
        }}
        searchExtra={[
          <div
            key="extra-buttons"
            style={{
              display: 'flex',
              flexGrow: 'wrap',
              gap: '8px',
              marginLeft: '-324px',
              marginTop: '40px'
            }}>
            <Button
              type="default"
              onClick={() => {
                cancelOrderItemsRequests()
                cancelRequest('anchorsOrdersKey')
                handleSelectChange('nickName', setPlaceholder)
                formRef.current?.resetFields()

                resetData(formRef, setData, setExtraParams)
              }}>
              {intlTrans('common.Reset')}
            </Button>
            <Button
              type="primary"
              onClick={() => {
                resetData(formRef, setData)
              }}>
              {intlTrans('common.Search')}
            </Button>
            {tabIndex == '2000' && (
              <div
                className={styles.filter_btn}
                onClick={() => {
                  setIsFilterDrawerOpen(true)
                }}>
                <FilterOutlined />
                {intlTrans('sample.筛选')}
                {filterNumber ? (
                  <div className={styles.filterNumber_box}>{filterNumber}</div>
                ) : null}
              </div>
            )}
            <br />
            {tabIndex == '2001' ? (
              <DownLoadBtn
                key="export"
                params={formRef.current}
                childrenType={21}
                onOk={() => {
                  formRef.current?.submit()
                }}
              />
            ) : null}
          </div>
        ]}
        columns={isNestedColumns(tabIndex) ? columns1 : columns2}
        dataSource={data || []}
        expandable={
          isNestedColumns(tabIndex)
            ? {
                expandIconColumnIndex: 4,
                expandedRowKeys: expandedRowKeys,
                expandedRowRender,
                onExpand: handleOnExpand,
                expandIcon: ({ expanded, onExpand, record }) => {
                  record.expandIcon = expanded ? (
                    <UpOutlined onClick={(e) => onExpand(record, e)} />
                  ) : (
                    <DownOutlined onClick={(e) => onExpand(record, e)} />
                  )
                  return null
                }
              }
            : undefined
        }
        rowKey="memberId"
        pagination={{
          pageSize: wrapperPageSize,
          size: 'default',
          showSizeChanger: false,
          showTotal(total, range) {
            return `${intlTrans('locale.total')} ${total}`
          }
        }}
      />
      <DrawerModal
        isDrawerModalOpen={isDrawerModalOpen}
        showDrawer={(e: any, num: any) => {
          handleFilterOk(e, num)
          setIsFilterDrawerOpen(false)
        }}
        handleFilterCancel={() => {
          setIsFilterDrawerOpen(false)
        }}
      />
      <RefuseModal
        isRefuseModalOpen={isRefuseModalOpen}
        setIsRefuseModalOpen={setIsRefuseModalOpen}
        handleOk={() => {
          handleRefuseOk()
        }}
        handleCancel={() => {
          setIsRefuseModalOpen(false)
        }}
        item={currentItem}
      />
      <ShipModal
        shipModalVisable={shipModalVisable}
        onConfirmShipModal={() => {
          requestSubListAndUpdateData(currentItem.accountId)
        }}
        item={currentItem}
        onCloseShipModal={() => {
          setShipModalVisable(false)
        }}
      />
      <ShowReciptInfoModal
        deliveryAddress={currentItem?.deliveryAddress}
        setShowReciptInfoModalVisable={setShowReciptInfoModalVisible}
        showReciptInfoModalVisable={showReciptInfoModalVisible}
      />
      <ShowLogisticsModal
        item={currentItem}
        showLogisticsModalVisable={showLogisticsModalVisible}
        setShowLogisticsModalVisable={setShowLogisticsModalVisible}
      />
      <PerformanceModal
        performanceVisables={performanceVisables}
        setPerformanceVisables={setPerformanceVisables}
        item={currentItem}
      />
    </div>
  )
}

export default withPageTitle(SampleApply, intlTrans('sample.样品申请'))
