import type { AxiosRequestConfig, AxiosResponse, CancelTokenSource } from 'axios'
import qs from 'qs'
import request from './axios'
import axios from 'axios'

// "application/x-www-form-urlencoded;charset=UTF-8"
// "application/json"

const cancelTokens: Record<string, (message?: string) => void> = {}
export const cancelRequest = (requestKey: string) => {
  if (typeof cancelTokens[requestKey] === 'function') {
    cancelTokens[requestKey]() // 取消请求
    delete cancelTokens[requestKey] // 删除
  }
}
// 取消所有请求
export const cancelAllRequests = () => {
  Object.keys(cancelTokens).forEach((key) => {
    cancelTokens[key]()
    delete cancelTokens[key]
  })
}

export const cancelOrderItemsRequests = () => {
  Object.keys(cancelTokens).forEach((key) => {
    if (key.indexOf('ordersItems_') !== -1) {
      cancelTokens[key]()
      delete cancelTokens[key]
    }
  })
}

class Http {
  static instance: Http | null = null
  static getInstance() {
    if (Http.instance) {
      return Http.instance
    }
    Http.instance = new Http()
    return Http.instance
  }

  async createRequest<P = any>(config: AxiosRequestConfig): Promise<P> {
    const response: AxiosResponse<P> = await request(config)
    return response.data
  }

  get<T = any, P = any>(
    url: string,
    params?: T,
    headers?: Record<string, any>,
    responseType?: any,
    requestKey?: string,
    noShowError?: boolean
  ): Promise<P> {
    const cancelTokenSource = axios.CancelToken.source()
    if (requestKey) {
      cancelTokens[requestKey] = () => cancelTokenSource.cancel() // ✅ 赋值正确的取消函数
    }

    return this.createRequest<P>({
      method: 'GET',
      url,
      params,
      headers,
      responseType,
      paramsSerializer: (params) => qs.stringify(params),
      cancelToken: cancelTokenSource.token,
      noShowError: noShowError
    }).finally(() => {
      if (requestKey) {
        delete cancelTokens[requestKey] // 请求完成后删除 key
      }
    })
  }

  post<T = any, P = any>(url: string, data?: T, headers?: Record<string, any>): Promise<P> {
    const contentType = headers ? headers['Content-Type'] : ''
    return this.createRequest<P>({
      method: 'POST',
      url,
      data: contentType.includes('application/json') ? data : qs.stringify({ ...data }),
      headers: headers
    })
  }

  postJSON<T = any, P = any>(url: string, data?: T): Promise<P> {
    return this.createRequest<P>({
      method: 'POST',
      url,
      data: Object.prototype.toString.call(data) === '[object Object]' ? { ...data } : data,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  upload<T = any, P = any>(url: string, formData?: T): Promise<P> {
    return this.createRequest<P>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  exportGet<T = any, P = any>(url: string, params?: T, headers?: any): Promise<P> {
    return this.createRequest<P>({
      method: 'GET',
      url,
      responseType: 'blob',
      params: {
        ...params
        // AppName
      },
      headers
    })
  }
}

export default Http.getInstance()
