import { message } from 'antd'
import type { RcFile } from 'antd/lib/upload'
import dayjs from 'dayjs'
import { getIntl, history } from 'umi'

export const delay = (time: number) =>
  new Promise((resolve) => {
    setTimeout(() => {
      resolve(false)
    }, time)
  })

export const isBuildProduction: boolean = process.env.NODE_ENV === 'production'

export const isProduction: boolean = window.location.host === 'ops.uchoice.pro'

export const openNewWindow = (pathname: string): void => {
  window.open(window.location.origin + history.createHref({ pathname }))
}

export const refreshGoPath = (pathname: string): void => {
  window.open(window.location.origin + history.createHref({ pathname }), '_self')
}

export function formatTimeForDayjs(time: any, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) {
    return '-'
  }
  return dayjs(time).format(format)
}

// excel字节流解析
export function excelParse(res: any, prefix?: string, name?: string) {
  const blob = new Blob([res.data])
  if (blob.size === 0) {
    return
  }
  const date = formatTimeForDayjs(new Date())
  const fileName = name || `${prefix || ''}${date}.xls`
  if (
    res.data.type === 'application/vnd.ms-excel' ||
    res.data.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ) {
    if ('download' in document.createElement('a')) {
      // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else {
      // IE10+下载
      ;(navigator as any).msSaveBlob(blob, fileName)
    }
  } else {
    const reader: any = new FileReader()
    reader.onload = function (event: any) {
      const msg = JSON.parse(reader.result).message
      message.warning(msg) // 将错误信息显示出来
    }
    reader.readAsText(res.data)
  }
}

export const closeCurrentPage = () => {
  const win: any = window.open('about:blank', '_self')
  win.close()
}

export const handleLocaleTime = (time: number) => {
  return time + (new Date().getTimezoneOffset() / 60 / -1 - 7) * 60 * 60 * 1000
}

/**
 * 判断文件是否是图片
 * @param fileType 文件类型
 * @returns {boolean}
 */
export const isImageFile = (fileType: string) => {
  return fileType.startsWith('image/')
}

export const intlTrans = (id: string) => {
  return getIntl().formatMessage({ id })
}

export const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = (error) => reject(error)
  })

export const replaceExceptNumber = (val: string) => {
  return val.replaceAll(/[^0-9]+/g, '')
}

export const formatNumber = (num: number = 0) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'
  }
  return num.toString()
}
