import { useIntl } from "umi";

export const useRequestErrorMsg = () => {
  const intl = useIntl();
  return (code: number) => {
    return intl.formatMessage({ id: `request.${code}` });
  };
};

export const useWorkOrderStatus = () => {
  const intl = useIntl();
  return (
    status: API.TWorkOrderStatus | undefined,
    type: API.IWorkOrderItemProps["type"] | undefined
  ) => {
    switch (status) {
      case 0:
        return intl.formatMessage({ id: "feedback.待受理" });
      case 1:
        return type === 0
          ? intl.formatMessage({ id: "feedback.处理中" })
          : intl.formatMessage({ id: "feedback.已接收" });
      case 2:
        return intl.formatMessage({ id: "feedback.已完结" });
      default:
        return "-";
    }
  };
};
