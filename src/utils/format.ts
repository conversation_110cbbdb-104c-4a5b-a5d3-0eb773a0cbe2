import dayjs from 'dayjs'
import { getLocale } from 'umi'
import localeData from 'dayjs/plugin/localeData'
import weekday from 'dayjs/plugin/weekday'
import moment from 'moment'
import { isTHRegion, isVIRegion } from './storage'
import { intlTrans } from './common'

export const dayjsPlus = dayjs.extend(weekday, localeData)

export const convertToThailandTimestamp = (
  date?: any,
  isEndOfMonth = false,
  type: 'day' | 'month' | 'year' = 'day'
) => {
  const timezone = 'Asia/Bangkok'
  const validDate = date ? moment(date) : moment()
  // 根据是否是结束时间来判断是获取该月的最后一天，还是第一天
  if (isEndOfMonth) {
    // 如果是结束时间，设置为该月最后一天的 23:59:59
    return validDate
      .tz(timezone) // 设置时区为泰国
      .endOf(type) // 获取该时间单位的结束时间（如日、月、年等）
      .valueOf() // 获取时间戳
  }

  // 否则设置为该月第一天的 00:00:00
  return validDate
    .tz(timezone) // 设置时区为泰国
    .startOf(type) // 获取该时间单位的起始时间
    .valueOf() // 获取时间戳
}
export function formatTime(
  time: number | undefined | null,
  format: string = 'YYYY/MM/DD HH:mm:ss'
) {
  if (!time) {
    return '-'
  }
  return dayjs(time).format(format)
}
export function formatThTime(
  time: number | undefined | null,
  format: string = 'YYYY/MM/DD HH:mm:ss'
) {
  if (!time) {
    return '-'
  }
  return moment(time).tz('Asia/Bangkok').format(format)
}
export function formatHours(hours: any) {
  if (hours < 24) {
    return `${hours} ${intlTrans('sample.小时')}`
  }
  const days = Math.floor(hours / 24)
  const remainingHours = hours % 24
  return remainingHours === 0
    ? `${days} ${intlTrans('sample.天')}`
    : `${days} ${intlTrans('sample.天')} ${remainingHours} ${intlTrans('sample.小时')}`
}

export function formatTimeYYMMDD(time: number) {
  return formatTime(time, 'YYYY-MM-DD')
}

export const formatUnit = (num?: number, placeholder = '', isNeedZero = false) => {
  if (!num && !isNeedZero) {
    return placeholder
  }
  if (num || num === 0) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }
}

export function formatTimeDay(time: number) {
  return formatTime(time, 'HH:mm:ss')
}
export function formatTimeDayYear(time: number) {
  return formatTime(time, 'ssmmHH_DDMMYYYY')
}
export function langPrefix(obj: Record<string, string>, str: string) {
  const result: Record<string, string> = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result[str + '.' + key] = obj[key]
    }
  }
  return result
}
export function formatSales(val = 0) {
  const num = Number(val).toString()
  if (isVIRegion()) {
    return `${num.replace(/(\d)(?=(\d{3})+$)/g, '$1.')}`
  }
  return `${num.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}
export function formatMoney(val = 0, unit?: boolean) {
  const num = Number(val).toFixed(2).toString()
  let result = num
  const [zheng, decimals] = num.split('.')
  if (num.length > 5) {
    result = `${zheng.replace(/(\d)(?=(\d{3})+$)/g, '$1,')}.${decimals}`
  }
  return unit ? '฿' + result : result
}
// 小数点
// 小数点

export function formatPrice(price: any, unit?: boolean) {
  if (Number.isNaN(+price) || price === '') {
    if (isVIRegion()) {
      return unit ? '0.00' + '₫' : unit
    }
    return unit ? '฿' + '0.00' : unit
  }
  if (isVIRegion()) {
    return unit
      ? `${(Math.floor(price) + '').replace(/(\d)(?=(\d{3})+$)/g, '$1.')}₫`
      : `${(Math.floor(price) + '').replace(/(\d)(?=(\d{3})+$)/g, '$1.')}`
  }
  return unit
    ? `฿${price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
    : `${price.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}
export function currencyUnit(price: any) {
  if (isTHRegion()) {
    return '฿' + price
  }
  return price + '₫'
}

export function formatNum(val = 0) {
  return `${(val + '').replace(/(\d)(?=(\d{3})+$)/g, '$1,')}`
}
// 时间戳转换成那天的第一秒
export function timestampToTodayStart(timestamp: any) {
  const yyyymmdd = new Date(timestamp).toISOString().split('T')[0]
  const year = yyyymmdd.split('-')[0]
  const month = yyyymmdd.split('-')[1]
  const day = yyyymmdd.split('-')[2]
  return new Date(`${month}/${day}/${year} 00:00:00`).getTime()
}

// 时间戳转换成那天的最后一秒
export function timestampToTodayEnd(timestamp: any) {
  const yyyymmdd = new Date(timestamp).toISOString().split('T')[0]
  const year = yyyymmdd.split('-')[0]
  const month = yyyymmdd.split('-')[1]
  const day = yyyymmdd.split('-')[2]
  return new Date(`${month}/${day}/${year} 23:59:59`).getTime()
}
// 示例方法，没有实际意义
export function trim(str: string) {
  return str.trim()
}

// 移除非数字字符串
export function converToNumber(value: string) {
  let res = value.replace(/\D/g, '')
  if (res.startsWith('0')) {
    res = res.replace(/0+/g, '0')
  }
  return res
}

// 自定义的月份缩写映射
const monthAbbr: any = {
  'en-US': [
    'Jan.',
    'Feb.',
    'Mar.',
    'Apr.',
    'May',
    'Jun.',
    'Jul.',
    'Aug.',
    'Sep.',
    'Oct.',
    'Nov.',
    'Dec.'
  ],
  'th-TH': [
    'ม.ค.',
    'ก.พ.',
    'มี.ค.',
    'เม.ย.',
    'พ.ค.',
    'มิ.ย.',
    'ก.ค.',
    'ส.ค.',
    'ก.ย.',
    'ต.ค.',
    'พ.ย.',
    'ธ.ค.'
  ],
  'vi-VN': [
    'Thg 1',
    'Thg 2',
    'Thg 3',
    'Thg 4',
    'Thg 5',
    'Thg 6',
    'Thg 7',
    'Thg 8',
    'Thg 9',
    'Thg 10',
    'Thg 11',
    'Thg 12'
  ],
  'zh-CN': ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
}

export const formatDate = (date: any) => {
  const language: any = getLocale()
  const day = dayjs(date).format('DD')
  const month = dayjs(date).month() // 0-based month
  const year = dayjs(date).format('YYYY')

  if (!monthAbbr[language]) {
    throw new Error(`Language '${language}' is not supported.`)
  }

  return language === 'zh-CN'
    ? dayjs(date).format('YYYY/MM/DD')
    : `${day} ${monthAbbr[language][month]} ${year}`
}

export const formatDateTime = (date: any) => {
  const language: any = getLocale()
  const day = dayjs(date).format('DD')
  const month = dayjs(date).month() // 0-based month
  const year = dayjs(date).format('YYYY')
  const time = dayjs(date).format('HH:mm:ss')

  if (!monthAbbr[language]) {
    throw new Error(`Language '${language}' is not supported.`)
  }

  return language === 'zh-CN'
    ? dayjs(date).format('YYYY/MM/DD HH:mm:ss')
    : `${day} ${monthAbbr[language][month]} ${year} ${time}`
}

export const formatToThailandTime = (time: number) => {
  return time + (new Date().getTimezoneOffset() / 60 / -1 - 7) * 60 * 60 * 1000
}

export function formatToTimestamp(datetimeStr = '', formatter = 'YYYY-MM-DD HH:mm:ss') {
  const formatParts = formatter.split(/[^A-Za-z]/)
  const dateParts = datetimeStr.split(/[^0-9]/)

  const formatMap: any = {}
  formatParts.forEach((part, index) => {
    formatMap[part] = dateParts[index]
  })

  const year = formatMap['YYYY'] || 0
  const month = (formatMap['MM'] || 1) - 1 // JavaScript 中的月份从0开始
  const day = formatMap['DD'] || 1
  const hours = formatMap['HH'] || 0
  const minutes = formatMap['mm'] || 0
  const seconds = formatMap['ss'] || 0

  const date = new Date(year, month, day, hours, minutes, seconds)
  const timestamp = date.getTime()

  return timestamp
}
