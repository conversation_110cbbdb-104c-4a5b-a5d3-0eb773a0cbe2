import { formatTimeYYMMDD, formatTimeDayYear } from '@/utils/format'
import { message } from 'antd'
import { useIntl, useHistory, useRouteMatch } from 'umi'

export function zipParse(data) {
  if (data.type === 'application/octet-stream') {
    // 获取http头部的文件名信息，若无需重命名文件，将下面这行删去
    const date = formatTimeYYMMDD(new Date())
    const fileName = `INV${date}`
    // res.headers['content-disposition'].split('=')[1]
    /* 兼容ie内核，360浏览器的兼容模式 */
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      const blob = new Blob([data], { type: 'application/zip' })
      window.navigator.msSaveOrOpenBlob(blob, fileName)
    } else {
      /* 火狐谷歌的文件下载方式 */
      const blob = new Blob([data], { type: 'application/zip' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a') // 创建a标签
      link.href = url
      link.download = fileName // 文件重命名，若无需重命名，将该行删去
      link.click()
      URL.revokeObjectURL(url) // 释放内存
    }
    // resolve(res)
  } else {
    const reader = new FileReader()
    reader.onload = function (event) {
      const msg = JSON.parse(reader.result).message
      message.warning(msg) // 将错误信息显示出来
    }
    reader.readAsText(data)
  }
}
// excel字节流解析
export function excelParse(res, name) {
  const blob = new Blob([res])
  const date = formatTimeDayYear(new Date())
  const fileName = `${name || ''}${date}.xls`
  if (
    res.type == 'application/vnd.ms-excel' ||
    res.type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ) {
    if ('download' in document.createElement('a')) {
      // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else {
      // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  } else {
    const reader = new FileReader()
    reader.onload = function (event) {
      const msg = JSON.parse(reader.result).message
      message.warning(msg) // 将错误信息显示出来
    }
    reader.readAsText(res.data)
  }
}

export default {
  zipParse,
  excelParse
}
