import { setLocale } from 'umi'

export const setLanguage = (nation: string) => {
  if (nation === 'VI') {
    setLocale('vi-VN', false)
  } else if (nation === 'TH') {
    setLocale('th-TH', false)
  }
}
export const setDefalutLang = () => {
  const userLanguage = navigator.language || 'en'
  if (userLanguage == 'th') {
    setLocale('th-TH', false)
  } else if (userLanguage == 'vi') {
    setLocale('vi-VN', false)
  } else {
    setLocale('en-US', false)
  }
}
