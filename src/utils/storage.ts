import { ULIVE_ADMIN_ENV_LOCALE_KEY } from '@/constants'

function createStorageObject<T>(key: string, options = {}) {
  return {
    get(): T | null {
      const str = localStorage.getItem(key)
      return str ? (JSON.parse(str) as T) : null
    },
    clear() {
      localStorage.removeItem(key)
    },
    set(value: T) {
      localStorage.setItem(key, JSON.stringify(value))
    },
    ...options
  }
}

export const nationStorage = {
  set(name: string) {
    localStorage.setItem('nation', name)
  },
  get() {
    return localStorage.getItem('nation')
  },
  remove() {
    localStorage.setItem('nation', '')
  }
}
//是否授权店铺
export const isAuthShopStorage = {
  set(isAuth: string) {
    localStorage.setItem('isAuthShop', isAuth)
  },
  get() {
    return localStorage.getItem('isAuthShop')
  },
  remove() {
    localStorage.setItem('isAuthShop', '')
  }
}
//待审核

export const isToAuditCountStorage = {
  set(isBeAudit: string) {
    localStorage.setItem('toBeAudit', isBeAudit)
  },
  get() {
    return localStorage.getItem('toBeAudit')
  },
  remove() {
    localStorage.setItem('toBeAudit', '')
  }
}
export const userInfoStorage = {
  set(userInfo: string) {
    localStorage.setItem('userInfo', userInfo)
  },
  get() {
    return localStorage.getItem('userInfo')
  },
  remove() {
    localStorage.setItem('userInfo', '')
  }
}
const STORAGE_KEY = 'shipInfoStorage'

export function setShipInfoStorage(userId: string, region: string, courierCompany: string) {
  const allData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}')

  if (!allData[userId]) {
    allData[userId] = {}
  }

  allData[userId][region] = {
    courierCompany
  }

  localStorage.setItem(STORAGE_KEY, JSON.stringify(allData))
}

export function getShipInfoStorage(
  userId: string,
  region: string
): { courierCompany: string } | null {
  const allData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}')
  return allData?.[userId]?.[region] || null
}

export const isTHRegion = () => {
  return nationStorage.get() === 'TH'
}

export const isVIRegion = () => {
  return nationStorage.get() === 'VI'
}

export const facebookPageTokenStorage = createStorageObject<string>('app_facebook_page_token')

export const apiEnvStorage = createStorageObject<TEnvEnmu>(ULIVE_ADMIN_ENV_LOCALE_KEY)
