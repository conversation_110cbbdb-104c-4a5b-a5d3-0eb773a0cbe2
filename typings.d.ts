declare module '*.css'
declare module '*.less'
declare module '*.png'
declare module '*.svg' {
  export function ReactComponent(props: React.SVGProps<SVGSVGElement>): React.ReactElement
  const url: string
  export default url
}

// 环境代表的值
type TEnvEnmu = 'DEV' | 'STAGING' | 'PROD' | 'PRE'

interface IRouteProps {
  redirect?: string
  name?: string
  path?: string
  component?: string
  routes?: IRouteProps[]
  hideInMenu?: boolean
  icon?: string
  layout?: boolean
  [filed: string]: any
}

interface IMWebpackContextStatic {
  (fileName: string): any
  keys: () => string[]
  id: string
}

interface IMRequire extends NodeRequire {
  context: (fileName: string, useSubdirectories: boolean, reg: RegExp) => IMWebpackContextStatic
}

interface IAppSettings {
  collapsed: boolean
}

interface IInitialStateProps {
  settings: IAppSettings
}

/**
 * 商品管理弹窗属性类型
 */
interface ProductManagementModalProps<T = any> {
  open: boolean
  onConfirm: (data: T, value?: any) => void | Promise<any>
  onCancel: () => void
  data?: T
}
